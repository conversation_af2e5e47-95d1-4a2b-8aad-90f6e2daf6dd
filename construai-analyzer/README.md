# ConstruAI Analyzer

Sistema de análise automatizada de projetos arquitetônicos usando IA.

## Arquitetura do Sistema

O ConstruAI Analyzer é uma plataforma completa para análise de projetos arquitetônicos que utiliza inteligência artificial para verificar conformidade com normas técnicas e regulamentos municipais.

```mermaid
graph TB
    %% Entry Points
    subgraph "🚀 Entry Points"
        MAIN[main.py<br/>CLI Interface]
        RUN[run_app.py<br/>Application Launcher]
    end

    %% Frontend Layer
    subgraph "🖥️ Frontend Layer"
        CHAT[chatbot_langchain_enhanced.py<br/>Main Application]
        ADMIN[admin_page.py<br/>Admin Interface]
        LANDING[landing_page.py<br/>Landing Page]
        LOGIN[login_page.py<br/>Authentication UI]
    end

    %% Authentication & Authorization
    subgraph "🔐 Authentication & Authorization"
        AUTH_MGR[auth_manager.py<br/>User Management]
        AUTH_APP[auth_app.py<br/>Auth Integration]
        SESSIONS[sessions.json<br/>Session Storage]
        USERS[users.json<br/>User Database]
    end

    %% Core Processing Engine
    subgraph "🧠 AI Processing Engine"
        PDF_ANALYZER[pdf_analyzer.py<br/>PDF Analysis]
        PROC_LLAMA[processamento_llama_langchain.py<br/>LangChain Processing]
        PROC_SIMPLE[processamento_simplificado.py<br/>Simple Processing]
        GEMINI[Google Gemini API<br/>AI Model]
    end

    %% Data Collection & Management
    subgraph "📊 Data Collection & Management"
        GEO_SAMPA[download_geosampa.py<br/>GeoSampa Data]
        COLETA[coletadados.py<br/>Data Collection]
        UPLOADS[uploads/<br/>User Files]
        TEMP[temp_uploads/<br/>Temporary Files]
        CACHE[analises/<br/>Analysis Cache]
    end

    %% Validation & Rules Engine
    subgraph "✅ Validation & Rules Engine"
        VALIDATOR[validator.py<br/>Data Validation]
        RULES[gestao_regras_regiao.py<br/>Regional Rules]
        COMPLIANCE[Compliance Checking<br/>Business Logic]
    end

    %% Report Generation
    subgraph "📄 Report Generation"
        REPORT_GEN[report_generator.py<br/>PDF Reports]
        CHARTS[Chart Generation<br/>Matplotlib]
        REPORTS[relatorios/<br/>Generated Reports]
    end

    %% External Services
    subgraph "🌐 External Services"
        GOOGLE_API[Google Generative AI<br/>API Service]
        GEOSAMPA_API[GeoSampa<br/>Municipal Data]
    end

    %% Data Storage
    subgraph "💾 Data Storage"
        JSON_DB[(JSON Files<br/>User Data)]
        FILE_STORAGE[(File System<br/>Documents & Cache)]
        EMBEDDINGS[(embeddings_db/<br/>Vector Storage)]
    end

    %% Flow Connections
    MAIN --> CHAT
    MAIN --> ADMIN
    MAIN --> LANDING
    RUN --> CHAT
    RUN --> ADMIN
    RUN --> LANDING

    CHAT --> AUTH_MGR
    ADMIN --> AUTH_MGR
    LOGIN --> AUTH_MGR
    AUTH_MGR --> SESSIONS
    AUTH_MGR --> USERS

    CHAT --> PDF_ANALYZER
    PDF_ANALYZER --> PROC_LLAMA
    PDF_ANALYZER --> PROC_SIMPLE
    PROC_LLAMA --> GEMINI
    GEMINI --> GOOGLE_API

    PDF_ANALYZER --> VALIDATOR
    VALIDATOR --> RULES
    VALIDATOR --> COMPLIANCE

    PDF_ANALYZER --> REPORT_GEN
    REPORT_GEN --> CHARTS
    REPORT_GEN --> REPORTS

    MAIN --> GEO_SAMPA
    MAIN --> COLETA
    GEO_SAMPA --> GEOSAMPA_API
    COLETA --> GEOSAMPA_API

    CHAT --> UPLOADS
    UPLOADS --> TEMP
    PDF_ANALYZER --> CACHE
    PROC_LLAMA --> EMBEDDINGS

    AUTH_MGR --> JSON_DB
    PDF_ANALYZER --> FILE_STORAGE
    CACHE --> FILE_STORAGE

    %% Styling
    classDef entryPoint fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef frontend fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef auth fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef processing fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef data fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef validation fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    classDef reports fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef external fill:#efebe9,stroke:#3e2723,stroke-width:2px
    classDef storage fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px

    class MAIN,RUN entryPoint
    class CHAT,ADMIN,LANDING,LOGIN frontend
    class AUTH_MGR,AUTH_APP,SESSIONS,USERS auth
    class PDF_ANALYZER,PROC_LLAMA,PROC_SIMPLE,GEMINI processing
    class GEO_SAMPA,COLETA,UPLOADS,TEMP,CACHE data
    class VALIDATOR,RULES,COMPLIANCE validation
    class REPORT_GEN,CHARTS,REPORTS reports
    class GOOGLE_API,GEOSAMPA_API external
    class JSON_DB,FILE_STORAGE,EMBEDDINGS storage
```

## Visão Geral

O ConstruAI Analyzer é uma plataforma SaaS que utiliza Inteligência Artificial para analisar projetos arquitetônicos e verificar sua conformidade com normas e regulamentos de construção. A plataforma extrai informações relevantes de projetos em PDF, como áreas, dimensões e especificações técnicas, e verifica automaticamente a conformidade com normas municipais.

## Funcionalidades Principais

- **Análise de Conformidade**: Verificação automática de conformidade com normas e regulamentos
- **Relatórios Detalhados**: Geração de relatórios em PDF com visualizações e recomendações
- **Interface Web**: Interface amigável para upload e análise de projetos
- **Autenticação de Usuários**: Sistema de login e registro de usuários
- **Coleta de Dados**: Coleta automática de dados de fontes públicas
- **Gestão de Regras Regionais**: Suporte a diferentes regulamentações por região
- **Processamento com IA**: Análise inteligente usando Google Gemini e LangChain

## Descrição dos Componentes

### 🚀 Entry Points (Pontos de Entrada)

- **main.py**: Interface CLI principal com argumentos para diferentes modos de operação
- **run_app.py**: Launcher da aplicação com verificação de ambiente e inicialização

### 🖥️ Frontend Layer (Camada de Interface)

- **chatbot_langchain_enhanced.py**: Interface principal do usuário para análise de projetos
- **admin_page.py**: Painel administrativo para gerenciamento de usuários e sistema
- **landing_page.py**: Página de apresentação do produto
- **login_page.py**: Interface de autenticação e registro de usuários

### 🔐 Authentication & Authorization (Autenticação e Autorização)

- **auth_manager.py**: Gerenciamento completo de usuários, autenticação e autorização
- **auth_app.py**: Integração do sistema de autenticação com a aplicação
- **sessions.json**: Armazenamento de sessões ativas
- **users.json**: Base de dados de usuários

### 🧠 AI Processing Engine (Motor de Processamento IA)

- **pdf_analyzer.py**: Análise principal de documentos PDF com extração de dados
- **processamento_llama_langchain.py**: Processamento avançado usando LangChain e LLMs
- **processamento_simplificado.py**: Processamento simplificado para casos básicos
- **Google Gemini API**: Modelo de IA para análise de texto e documentos

### 📊 Data Collection & Management (Coleta e Gestão de Dados)

- **download_geosampa.py**: Coleta automatizada de dados do portal GeoSampa
- **coletadados.py**: Coleta de dados de múltiplas fontes públicas
- **uploads/**: Armazenamento de arquivos enviados pelos usuários
- **temp_uploads/**: Armazenamento temporário durante processamento
- **analises/**: Cache de análises realizadas

### ✅ Validation & Rules Engine (Motor de Validação e Regras)

- **validator.py**: Validação de dados e estruturas de análise
- **gestao_regras_regiao.py**: Gerenciamento de regras específicas por região/cidade
- **Compliance Checking**: Lógica de negócio para verificação de conformidade

### 📄 Report Generation (Geração de Relatórios)

- **report_generator.py**: Geração de relatórios em PDF com análises detalhadas
- **Chart Generation**: Criação de gráficos e visualizações usando Matplotlib
- **relatorios/**: Armazenamento de relatórios gerados

## Fluxo de Dados

1. **Entrada**: Usuário faz upload de PDF através da interface web
2. **Autenticação**: Sistema verifica credenciais e permissões
3. **Processamento**: PDF é analisado usando IA (Google Gemini + LangChain)
4. **Validação**: Dados extraídos são validados contra regras regionais
5. **Análise**: Sistema verifica conformidade com normas técnicas
6. **Relatório**: Geração de relatório detalhado com visualizações
7. **Armazenamento**: Resultados são salvos em cache para consultas futuras

## Tecnologias Utilizadas

- **Python 3.10+**: Linguagem principal
- **Streamlit**: Interface web interativa
- **LangChain**: Framework para aplicações com IA
- **Google Gemini**: Modelo de linguagem para análise
- **PyPDF**: Processamento de arquivos PDF
- **ReportLab**: Geração de relatórios em PDF
- **Matplotlib**: Criação de gráficos e visualizações
- **Pydantic**: Validação de dados e schemas
- **FastAPI**: API backend (opcional)
- **Celery**: Processamento assíncrono de tarefas

## Requisitos

- Python 3.10+
- Google Gemini API Key (já configurada no arquivo `.env` de exemplo)
- Dependências listadas em `requirements.txt`

## Instalação

### Pré-requisitos

- Python 3.10 ou superior
- Chave de API do Google Gemini

### Passos de Instalação

1. Clone o repositório:

```bash
git clone https://github.com/seu-usuario/construai-analyzer.git
cd construai-analyzer
```

2. Crie e ative um ambiente virtual:

```bash
python -m venv venv
source venv/bin/activate  # No Windows: venv\Scripts\activate
```

3. Instale as dependências:

```bash
pip install -r requirements.txt
```

4. Configure as variáveis de ambiente:

```bash
# O arquivo .env já está configurado com uma chave de API do Google Gemini para testes
# Se necessário, edite o arquivo .env com suas próprias credenciais
```

### Configuração do Arquivo .env

Crie um arquivo `.env` na raiz do projeto com as seguintes variáveis:

```env
# API Keys
GOOGLE_API_KEY=sua_chave_do_google_gemini

# Configurações do banco de dados (opcional)
DATABASE_URL=sqlite:///./construai.db

# Configurações de autenticação
SECRET_KEY=sua_chave_secreta_aqui
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Configurações do Celery (opcional)
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
```

## Uso

### Modos de Execução

O sistema oferece diferentes modos de execução através dos pontos de entrada:

#### 1. Aplicação Principal (Recomendado)

```bash
# Usando o launcher principal
python run_app.py

# Ou diretamente com Streamlit
streamlit run chatbot_langchain_enhanced.py

# Ou usando o CLI
python main.py --chatbot
```

#### 2. Página de Administração

```bash
python run_app.py --admin
# ou
streamlit run admin_page.py
```

#### 3. Landing Page

```bash
python run_app.py --landing
# ou
streamlit run landing_page.py
```

#### 4. Coleta de Dados

```bash
python main.py --collect
```

#### 5. Processamento de Documentos

```bash
python main.py --process
```

#### 6. Pipeline Completo

```bash
python main.py --all
```

### Acesso à Interface Web

Após executar a aplicação, acesse:

- **Aplicação principal**: http://localhost:8501
- **Administração**: http://localhost:8501 (com --admin)
- **Landing page**: http://localhost:8501 (com --landing)

### Credenciais de Acesso

O sistema já vem com um usuário administrador pré-configurado:

- **Email**: <EMAIL>
- **Senha**: admin123

### Como Analisar um Projeto

1. **Login**: Faça login no sistema usando as credenciais
2. **Upload**: Faça upload de um arquivo PDF na seção "Upload de Projeto"
3. **Processamento**: Aguarde a análise automática (pode levar até 3 minutos)
4. **Resultados**: Explore os resultados na seção "Resultados da Análise"
5. **Interação**: Faça perguntas sobre o projeto na seção de chat
6. **Relatório**: Baixe o relatório detalhado em PDF

### Arquivos de Exemplo

O diretório `projetos_data/pdfs/` contém arquivos de exemplo para teste:

- `exemplo_projeto.pdf`: Projeto básico para testes
- `projeto_residencial_completo.pdf`: Projeto residencial completo
- `projeto_comercial_completo.pdf`: Projeto comercial
- `projeto_industrial.pdf`: Projeto industrial
- `projeto_nao_conforme.pdf`: Projeto com problemas de conformidade

## Estrutura do Projeto

```
construai-analyzer/
├── 🚀 Entry Points
│   ├── main.py                      # CLI principal com argumentos
│   └── run_app.py                   # Launcher da aplicação
├── 🖥️ Frontend Layer
│   ├── chatbot_langchain_enhanced.py # Interface principal do usuário
│   ├── admin_page.py                # Painel administrativo
│   ├── landing_page.py              # Landing page do produto
│   └── login_page.py                # Interface de autenticação
├── 🔐 Authentication & Authorization
│   ├── auth_manager.py              # Gerenciamento de usuários
│   ├── auth_app.py                  # Integração de autenticação
│   ├── sessions.json                # Armazenamento de sessões
│   └── users.json                   # Base de dados de usuários
├── 🧠 AI Processing Engine
│   ├── pdf_analyzer.py              # Análise principal de PDFs
│   ├── processamento_llama_langchain.py # Processamento LangChain
│   └── processamento_simplificado.py    # Processamento simplificado
├── 📊 Data Collection & Management
│   ├── download_geosampa.py         # Coleta dados GeoSampa
│   ├── coletadados.py               # Coleta dados múltiplas fontes
│   ├── uploads/                     # Arquivos enviados pelos usuários
│   ├── temp_uploads/                # Armazenamento temporário
│   └── analises/                    # Cache de análises
├── ✅ Validation & Rules Engine
│   ├── validator.py                 # Validação de dados
│   └── gestao_regras_regiao.py      # Regras regionais
├── 📄 Report Generation
│   ├── report_generator.py          # Geração de relatórios PDF
│   └── relatorios/                  # Relatórios gerados
├── 🧪 Testing & Development
│   ├── test_gemini.py               # Teste API Google Gemini
│   ├── test_pdf_analyzer.py         # Teste analisador PDFs
│   ├── test_validator.py            # Teste validador
│   └── test_*.py                    # Outros testes
├── 📁 Data & Configuration
│   ├── requirements.txt             # Dependências Python
│   ├── .env                         # Variáveis de ambiente
│   ├── projetos_data/               # Dados coletados e exemplos
│   │   └── pdfs/                    # Arquivos PDF de exemplo
│   ├── embeddings_db/               # Armazenamento de vetores
│   └── frontend/                    # Frontend adicional (React)
└── 📋 Documentation
    └── README.md                    # Este arquivo
```

## Padrões de Desenvolvimento

### Arquitetura em Camadas

O sistema segue uma arquitetura em camadas bem definida:

- **Apresentação**: Interfaces Streamlit para diferentes usuários
- **Aplicação**: Lógica de negócio e orquestração de serviços
- **Domínio**: Regras de negócio e validações específicas
- **Infraestrutura**: Integração com APIs externas e armazenamento

### Princípios SOLID

- **Single Responsibility**: Cada módulo tem uma responsabilidade específica
- **Open/Closed**: Sistema extensível para novas regras regionais
- **Liskov Substitution**: Interfaces consistentes entre processadores
- **Interface Segregation**: Interfaces específicas para cada tipo de usuário
- **Dependency Inversion**: Dependências abstraídas através de interfaces

### Padrões de Design

- **Strategy Pattern**: Diferentes estratégias de processamento (LangChain vs Simplificado)
- **Factory Pattern**: Criação de validadores específicos por região
- **Observer Pattern**: Sistema de notificações e logs
- **Command Pattern**: Execução de diferentes modos via CLI

## Solução de Problemas

### Problemas Comuns

#### Erro ao iniciar a aplicação

Se você encontrar erros ao iniciar a aplicação:

```bash
# Tente usar diretamente o Streamlit
streamlit run chatbot_langchain_enhanced.py

# Ou verifique as dependências
pip install -r requirements.txt
```

#### Erro com o modelo Gemini

Se você encontrar erros relacionados ao modelo Gemini:

1. Verifique se a chave da API do Google está configurada no arquivo `.env`
2. Confirme se você tem conexão com a internet
3. Execute o script de teste: `python test_gemini.py`

#### Erro ao processar PDFs

Se você encontrar erros ao processar PDFs:

1. Verifique se o arquivo PDF é válido e não está corrompido
2. Confirme se o arquivo PDF contém texto (não apenas imagens)
3. Execute o script de teste: `python test_pdf_analyzer.py`

#### Problemas de Memória

Para projetos grandes, ajuste as configurações:

```python
# No arquivo de configuração
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
CHUNK_SIZE = 1000  # Tamanho dos chunks para processamento
```

## Modelo de Negócio

### Planos de Assinatura

- **Plano Básico**: R$ 299/projeto analisado
- **Plano Premium**: R$ 1.999/mês para análises ilimitadas
- **Plano Corporativo**: Preços personalizados para construtoras

### Funcionalidades por Plano

- **Básico**: Análise de conformidade básica, relatórios simples
- **Premium**: Análise avançada, relatórios detalhados, suporte prioritário
- **Corporativo**: API dedicada, integração personalizada, SLA garantido

## Roadmap

### Concluído ✅

- [x] Coleta de dados de fontes públicas
- [x] Processamento básico de documentos
- [x] Interface web com autenticação
- [x] Geração de relatórios em PDF
- [x] Landing page do produto
- [x] Sistema de validação regional

### Em Desenvolvimento 🚧

- [ ] Integração com sistemas CAD
- [ ] Detecção avançada de problemas estruturais
- [ ] API REST completa
- [ ] Dashboard de análises para empresas

### Planejado 📋

- [ ] Suporte a mais cidades e regulamentos
- [ ] Integração com sistemas de aprovação municipal
- [ ] Análise de imagens e plantas baixas
- [ ] Sistema de notificações em tempo real

## Contribuição

Contribuições são bem-vindas! Para contribuir:

1. Faça um fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

### Diretrizes de Contribuição

- Siga os padrões de código existentes
- Adicione testes para novas funcionalidades
- Atualize a documentação conforme necessário
- Use mensagens de commit descritivas

## Licença

Este projeto está licenciado sob a licença MIT - veja o arquivo LICENSE para detalhes.

## Contato

Para mais informações:

- **Email**: <EMAIL>
- **Website**: https://construai.com.br
- **Suporte**: <EMAIL>

---

**ConstruAI Analyzer** - Transformando a análise de projetos arquitetônicos com Inteligência Artificial 🏗️🤖
