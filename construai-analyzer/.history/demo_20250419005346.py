#!/usr/bin/env python3
"""
ConstruAI Analyzer - Script de Demonstração
Este script demonstra o fluxo completo de análise de projetos arquitetônicos.
"""

import os
import sys
import time
import logging
import argparse
from dotenv import load_dotenv

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("demo.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("demo")

# Carregar variáveis de ambiente
load_dotenv()

def print_header(title):
    """Imprime um cabeçalho formatado."""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80 + "\n")

def print_step(step, description):
    """Imprime um passo do processo."""
    print(f"\n[PASSO {step}] {description}")
    print("-" * 80)

def run_demo(pdf_path, open_browser=True):
    """Executa a demonstração completa do ConstruAI Analyzer."""
    print_header("ConstruAI Analyzer - Demonstração")
    print("Este script demonstra o fluxo completo de análise de projetos arquitetônicos.")
    print("Desenvolvido por: ConstruAI Team")
    print("\nIniciando demonstração...\n")

    # Verificar se o arquivo PDF existe
    if not os.path.exists(pdf_path):
        print(f"ERRO: O arquivo {pdf_path} não foi encontrado.")
        return False

    # PASSO 1: Analisar o projeto
    print_step(1, "Analisando o projeto arquitetônico")
    print(f"Arquivo: {pdf_path}")

    try:
        from pdf_analyzer import ProjectAnalyzer

        # Iniciar cronômetro
        start_time = time.time()

        # Analisar o projeto
        analyzer = ProjectAnalyzer()
        result = analyzer.analyze_project(pdf_path)

        # Calcular tempo de processamento
        processing_time = time.time() - start_time

        print(f"✅ Análise concluída em {processing_time:.2f} segundos!")

        # Exibir resultados básicos
        if "project_data" in result and result["project_data"]:
            project_data = result["project_data"]
            print("\nDados extraídos do projeto:")
            print(f"- Área do terreno: {project_data.get('area_terreno', 'N/A')} m²")
            print(f"- Área construída: {project_data.get('area_construida', 'N/A')} m²")
            print(f"- Taxa de ocupação: {project_data.get('taxa_ocupacao', 'N/A')}%")
            print(f"- Zoneamento: {project_data.get('zoneamento', 'N/A')}")
            print(f"- Tipo de construção: {project_data.get('tipo_construcao', 'N/A')}")

        # Exibir resultados de conformidade
        if "compliance" in result and result["compliance"]:
            compliance = result["compliance"]
            if compliance.get("em_conformidade", False):
                print("\n✅ O projeto está em conformidade com as normas!")
            else:
                print("\n❌ O projeto apresenta problemas de conformidade:")
                for problema in compliance.get("problemas_identificados", []):
                    print(f"  - {problema}")

        # Salvar resultado em JSON
        import json
        output_dir = "analises"
        os.makedirs(output_dir, exist_ok=True)

        output_file = os.path.join(output_dir, f"{os.path.basename(pdf_path)}_analysis.json")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

        print(f"\nResultado completo salvo em: {output_file}")

    except Exception as e:
        print(f"❌ Erro ao analisar o projeto: {str(e)}")
        return False

    # PASSO 2: Gerar relatório
    print_step(2, "Gerando relatório de análise")

    try:
        from report_generator import generate_report

        report_path = os.path.join(output_dir, f"{os.path.basename(pdf_path)}_report.pdf")
        generate_report(result, report_path)

        if os.path.exists(report_path):
            print(f"✅ Relatório gerado com sucesso: {report_path}")
        else:
            print(f"❌ Erro ao gerar relatório. Verifique os logs para mais detalhes.")

    except ImportError:
        print("⚠️ Módulo de geração de relatórios não encontrado. Pulando este passo.")
    except Exception as e:
        print(f"❌ Erro ao gerar relatório: {str(e)}")

    # PASSO 3: Iniciar interface web
    print_step(3, "Iniciando interface web")

    if open_browser:
        try:
            import subprocess

            print("Iniciando servidor Streamlit...")
            process = subprocess.Popen(
                ["python", "main.py", "--chatbot"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # Esperar um pouco para o servidor iniciar
            time.sleep(5)

            # Verificar se o processo está rodando
            if process.poll() is None:
                print("✅ Servidor Streamlit iniciado com sucesso!")
                print("\nAcesse a interface web em: http://localhost:8501")
                print("\nPressione Ctrl+C para encerrar a demonstração quando terminar.")

                # Manter o script rodando
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\nEncerrando demonstração...")
                    process.terminate()
            else:
                stdout, stderr = process.communicate()
                print(f"❌ Erro ao iniciar servidor Streamlit: {stderr}")

        except Exception as e:
            print(f"❌ Erro ao iniciar interface web: {str(e)}")
    else:
        print("Pulando inicialização da interface web.")
        print("\nPara iniciar a interface web manualmente, execute:")
        print("  python main.py --chatbot")

    print_header("Demonstração Concluída")
    print("Obrigado por experimentar o ConstruAI Analyzer!")
    print("Para mais informações, visite: https://construai.com.br")

    return True

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="ConstruAI Analyzer - Script de Demonstração")
    parser.add_argument("pdf_path", nargs="?", default="projetos_data/pdfs/exemplo_projeto.pdf",
                        help="Caminho para o arquivo PDF do projeto (default: projetos_data/pdfs/exemplo_projeto.pdf)")
    parser.add_argument("--no-browser", action="store_true", help="Não abrir o navegador automaticamente")

    args = parser.parse_args()

    run_demo(args.pdf_path, not args.no_browser)
