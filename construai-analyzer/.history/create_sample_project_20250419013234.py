#!/usr/bin/env python3
"""
Script para criar um projeto de exemplo completo em PDF
"""

import os
from fpdf import FPDF
import random
import datetime

class ProjectPDF(FPDF):
    def __init__(self):
        super().__init__()
        self.set_auto_page_break(auto=True, margin=15)

    def header(self):
        # Título
        self.set_font('Arial', 'B', 15)
        self.cell(0, 10, 'PROJETO ARQUITETÔNICO - MEMORIAL DESCRITIVO', 0, 1, 'C')
        # Data
        self.set_font('Arial', 'I', 10)
        self.cell(0, 5, f'Data: {datetime.datetime.now().strftime("%d/%m/%Y")}', 0, 1, 'R')
        # Linha
        self.line(10, 25, 200, 25)
        self.ln(5)

    def footer(self):
        # Posição a 1.5 cm do final
        self.set_y(-15)
        # Fonte
        self.set_font('Arial', 'I', 8)
        # Númer<PERSON> da página
        self.cell(0, 10, f'Página {self.page_no()}/{{nb}}', 0, 0, 'C')
        # Copyright
        self.cell(0, 10, 'ConstruAI © 2023', 0, 0, 'R')

def create_sample_project(output_path, project_type="residencial", custom_data=None):
    """Cria um projeto de exemplo completo em PDF."""

    # Usar dados personalizados se fornecidos
    if custom_data is not None:
        project_data = custom_data
    # Caso contrário, definir dados do projeto com base no tipo
    elif project_type == "residencial":
        project_data = {
            "nome_projeto": "Residência Unifamiliar Silva",
            "endereco": "Rua das Flores, 123 - São Paulo/SP",
            "area_terreno": 250.0,
            "area_construida": 150.0,
            "taxa_ocupacao": 60.0,
            "coeficiente_aproveitamento": 1.2,
            "zoneamento": "ZM-3 (Zona Mista)",
            "tipo_construcao": "Residencial Unifamiliar",
            "num_pavimentos": 2,
            "altura_maxima": 7.5,
            "recuo_frontal": 5.0,
            "recuo_lateral": 1.5,
            "recuo_fundos": 3.0,
            "especificacoes_estruturais": {
                "vigas": "Concreto armado 15x40cm, fck=25MPa",
                "pilares": "Concreto armado 20x20cm, fck=25MPa",
                "fundacao": "Sapata isolada de concreto armado, fck=25MPa",
                "lajes": "Laje maciça de concreto armado, h=12cm, fck=25MPa"
            },
            "instalacoes": {
                "eletrica": "Entrada bifásica, 220V, 50A",
                "hidraulica": "Tubulação em PVC, reservatório de 1000L",
                "sanitaria": "Sistema de esgoto com fossa séptica e sumidouro"
            }
        }
    elif project_type == "comercial":
        project_data = {
            "nome_projeto": "Edifício Comercial Central",
            "endereco": "Avenida Paulista, 1000 - São Paulo/SP",
            "area_terreno": 500.0,
            "area_construida": 1500.0,
            "taxa_ocupacao": 65.0,
            "coeficiente_aproveitamento": 3.0,
            "zoneamento": "ZC (Zona Comercial)",
            "tipo_construcao": "Comercial",
            "num_pavimentos": 5,
            "altura_maxima": 18.0,
            "recuo_frontal": 5.0,
            "recuo_lateral": 2.0,
            "recuo_fundos": 3.0,
            "especificacoes_estruturais": {
                "vigas": "Concreto armado 20x60cm, fck=30MPa",
                "pilares": "Concreto armado 30x30cm, fck=30MPa",
                "fundacao": "Estacas pré-moldadas de concreto, fck=30MPa",
                "lajes": "Laje nervurada de concreto armado, h=25cm, fck=30MPa"
            },
            "instalacoes": {
                "eletrica": "Entrada trifásica, 380V, 100A",
                "hidraulica": "Tubulação em PVC, reservatório de 5000L",
                "sanitaria": "Sistema de esgoto com tratamento primário"
            }
        }
    else:
        project_data = {
            "nome_projeto": "Projeto Personalizado",
            "endereco": "Endereço do Projeto - São Paulo/SP",
            "area_terreno": random.uniform(200, 1000),
            "area_construida": random.uniform(100, 800),
            "taxa_ocupacao": random.uniform(40, 70),
            "coeficiente_aproveitamento": random.uniform(0.8, 4.0),
            "zoneamento": random.choice(["ZM-3 (Zona Mista)", "ZC (Zona Comercial)", "ZER (Zona Exclusivamente Residencial)"]),
            "tipo_construcao": random.choice(["Residencial Unifamiliar", "Residencial Multifamiliar", "Comercial", "Misto"]),
            "num_pavimentos": random.randint(1, 10),
            "altura_maxima": random.uniform(3, 30),
            "recuo_frontal": random.uniform(3, 6),
            "recuo_lateral": random.uniform(1, 3),
            "recuo_fundos": random.uniform(2, 5),
            "especificacoes_estruturais": {
                "vigas": f"Concreto armado {random.choice(['15x40cm', '20x50cm', '25x60cm'])}, fck={random.choice([25, 30, 35])}MPa",
                "pilares": f"Concreto armado {random.choice(['20x20cm', '25x25cm', '30x30cm'])}, fck={random.choice([25, 30, 35])}MPa",
                "fundacao": random.choice(["Sapata isolada", "Estacas pré-moldadas", "Radier"]) + f" de concreto armado, fck={random.choice([25, 30, 35])}MPa",
                "lajes": f"Laje {random.choice(['maciça', 'nervurada', 'treliçada'])} de concreto armado, h={random.choice([12, 15, 20, 25])}cm, fck={random.choice([25, 30, 35])}MPa"
            },
            "instalacoes": {
                "eletrica": f"Entrada {random.choice(['monofásica', 'bifásica', 'trifásica'])}, {random.choice([110, 220, 380])}V, {random.choice([30, 50, 75, 100])}A",
                "hidraulica": f"Tubulação em {random.choice(['PVC', 'PPR', 'Cobre'])}, reservatório de {random.choice([500, 1000, 2000, 5000])}L",
                "sanitaria": f"Sistema de esgoto com {random.choice(['fossa séptica', 'tratamento primário', 'ligação à rede pública'])}"
            }
        }

    # Criar PDF
    pdf = ProjectPDF()
    pdf.alias_nb_pages()
    pdf.add_page()

    # Informações do Projeto
    pdf.set_font('Arial', 'B', 12)
    pdf.cell(0, 10, '1. INFORMAÇÕES GERAIS DO PROJETO', 0, 1, 'L')

    pdf.set_font('Arial', '', 11)
    pdf.cell(0, 6, f"Nome do Projeto: {project_data['nome_projeto']}", 0, 1)
    pdf.cell(0, 6, f"Endereço: {project_data['endereco']}", 0, 1)
    pdf.cell(0, 6, f"Tipo de Construção: {project_data['tipo_construcao']}", 0, 1)
    pdf.cell(0, 6, f"Zoneamento: {project_data['zoneamento']}", 0, 1)
    pdf.ln(5)

    # Parâmetros Urbanísticos
    pdf.set_font('Arial', 'B', 12)
    pdf.cell(0, 10, '2. PARÂMETROS URBANÍSTICOS', 0, 1, 'L')

    pdf.set_font('Arial', '', 11)
    pdf.cell(0, 6, f"Área do Terreno: {project_data['area_terreno']} m²", 0, 1)
    pdf.cell(0, 6, f"Área Construída: {project_data['area_construida']} m²", 0, 1)
    pdf.cell(0, 6, f"Taxa de Ocupação: {project_data['taxa_ocupacao']}%", 0, 1)
    pdf.cell(0, 6, f"Coeficiente de Aproveitamento: {project_data['coeficiente_aproveitamento']}", 0, 1)
    pdf.cell(0, 6, f"Número de Pavimentos: {project_data['num_pavimentos']}", 0, 1)
    pdf.cell(0, 6, f"Altura Máxima: {project_data['altura_maxima']} m", 0, 1)
    pdf.ln(5)

    # Recuos
    pdf.set_font('Arial', 'B', 12)
    pdf.cell(0, 10, '3. RECUOS', 0, 1, 'L')

    pdf.set_font('Arial', '', 11)
    pdf.cell(0, 6, f"Recuo Frontal: {project_data['recuo_frontal']} m", 0, 1)
    pdf.cell(0, 6, f"Recuo Lateral: {project_data['recuo_lateral']} m", 0, 1)
    pdf.cell(0, 6, f"Recuo de Fundos: {project_data['recuo_fundos']} m", 0, 1)
    pdf.ln(5)

    # Especificações Estruturais
    pdf.set_font('Arial', 'B', 12)
    pdf.cell(0, 10, '4. ESPECIFICAÇÕES ESTRUTURAIS', 0, 1, 'L')

    pdf.set_font('Arial', '', 11)
    pdf.cell(0, 6, f"Vigas: {project_data['especificacoes_estruturais']['vigas']}", 0, 1)
    pdf.cell(0, 6, f"Pilares: {project_data['especificacoes_estruturais']['pilares']}", 0, 1)
    pdf.cell(0, 6, f"Fundação: {project_data['especificacoes_estruturais']['fundacao']}", 0, 1)
    pdf.cell(0, 6, f"Lajes: {project_data['especificacoes_estruturais']['lajes']}", 0, 1)
    pdf.ln(5)

    # Instalações
    pdf.set_font('Arial', 'B', 12)
    pdf.cell(0, 10, '5. INSTALAÇÕES', 0, 1, 'L')

    pdf.set_font('Arial', '', 11)
    pdf.cell(0, 6, f"Instalação Elétrica: {project_data['instalacoes']['eletrica']}", 0, 1)
    pdf.cell(0, 6, f"Instalação Hidráulica: {project_data['instalacoes']['hidraulica']}", 0, 1)
    pdf.cell(0, 6, f"Instalação Sanitária: {project_data['instalacoes']['sanitaria']}", 0, 1)
    pdf.ln(5)

    # Observações
    pdf.set_font('Arial', 'B', 12)
    pdf.cell(0, 10, '6. OBSERVAÇÕES', 0, 1, 'L')

    pdf.set_font('Arial', '', 11)
    pdf.multi_cell(0, 6, "Este projeto atende às normas e regulamentos da Prefeitura de São Paulo, incluindo o Código de Obras e Edificações (Lei nº 16.642/2017) e a Lei de Zoneamento (Lei nº 16.402/2016).")
    pdf.ln(5)

    # Responsável Técnico
    pdf.set_font('Arial', 'B', 12)
    pdf.cell(0, 10, '7. RESPONSÁVEL TÉCNICO', 0, 1, 'L')

    pdf.set_font('Arial', '', 11)
    pdf.cell(0, 6, "Nome: Eng. Civil João da Silva", 0, 1)
    pdf.cell(0, 6, "CREA: 123456/SP", 0, 1)
    pdf.cell(0, 6, "ART: 9876543210", 0, 1)

    # Salvar PDF
    pdf.output(output_path)
    print(f"Projeto de exemplo criado com sucesso: {output_path}")
    return project_data

if __name__ == "__main__":
    # Criar diretório para projetos de exemplo
    os.makedirs("projetos_data/pdfs", exist_ok=True)

    # Criar projeto residencial
    output_path = "projetos_data/pdfs/projeto_residencial_completo.pdf"
    create_sample_project(output_path, "residencial")

    # Criar projeto comercial
    output_path = "projetos_data/pdfs/projeto_comercial_completo.pdf"
    create_sample_project(output_path, "comercial")

    # Criar projeto aleatório
    output_path = "projetos_data/pdfs/projeto_aleatorio_completo.pdf"
    create_sample_project(output_path, "aleatorio")
