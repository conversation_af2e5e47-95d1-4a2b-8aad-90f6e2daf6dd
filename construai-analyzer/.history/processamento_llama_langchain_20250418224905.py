from llama_index import SimpleDirectoryReader, VectorStoreIndex, StorageContext
from llama_index.vector_stores import ChromaVectorStore
from llama_index.storage.docstore import SimpleDocumentStore
from llama_index.embeddings import OpenAIEmbedding
from langchain.llms import OpenAI
import chromadb
import os
import json
import logging
import time
from tqdm import tqdm
from dotenv import load_dotenv

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("processamento.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("processamento")

# Carregar variáveis de ambiente
load_dotenv()

# Configurações
projeto_dir = "projetos_pdf"
db_dir = "embeddings_db"
analysis_output = "analises"

# Criar diretórios necessários
os.makedirs(db_dir, exist_ok=True)
os.makedirs(analysis_output, exist_ok=True)

# Configurar cliente Chroma
chroma_client = chromadb.PersistentClient(db_dir)
collection = chroma_client.create_collection("projetos_construcao")

# Configurar armazenamento de documentos
docstore = SimpleDocumentStore()
storage_context = StorageContext.from_defaults(
    docstore=docstore,
    vector_store=ChromaVectorStore(chroma_collection=collection)
)

# Carregar documentos
print("Carregando documentos...")
documents = SimpleDirectoryReader(projeto_dir).load_data()
print(f"Carregados {len(documents)} documentos")

# Criar índice vetorial
print("Criando índice de documentos...")
index = VectorStoreIndex.from_documents(
    documents,
    storage_context=storage_context,
    embed_model=OpenAIEmbedding()
)

# Motor de consulta
query_engine = index.as_query_engine()

# Funções de análise
def verificar_taxa_ocupacao(doc_id):
    """Verifica se a taxa de ocupação está dentro dos limites permitidos."""
    resposta = query_engine.query(
        f"Para o documento {doc_id}, extraia a taxa de ocupação projetada e compare com o limite de 70% permitido. " +
        "Explique se está em conformidade."
    )
    return resposta.response

def verificar_zoneamento(doc_id):
    """Verifica se o projeto é compatível com o zoneamento da área."""
    resposta = query_engine.query(
        f"Para o documento {doc_id}, identifique o zoneamento da área e verifique se o tipo de construção " +
        "proposto é permitido nesse zoneamento."
    )
    return resposta.response

def verificar_calculos_estruturais(doc_id):
    """Verifica se os cálculos estruturais estão corretos."""
    resposta = query_engine.query(
        f"Para o documento {doc_id}, analise as especificações das vigas e pilares em relação à carga prevista. " +
        "Identifique possíveis erros ou inconsistências nos cálculos estruturais."
    )
    return resposta.response

# Processamento em lote
for i, doc in enumerate(tqdm(documents)):
    doc_id = doc.doc_id

    # Realizar análises
    resultado = {
        "doc_id": doc_id,
        "nome_arquivo": doc.metadata.get("file_name", ""),
        "analises": {
            "taxa_ocupacao": verificar_taxa_ocupacao(doc_id),
            "zoneamento": verificar_zoneamento(doc_id),
            "calculos_estruturais": verificar_calculos_estruturais(doc_id)
        }
    }

    # Salvar resultado
    with open(f"{analysis_output}/{doc_id}.json", 'w', encoding='utf-8') as f:
        json.dump(resultado, f, ensure_ascii=False, indent=2)