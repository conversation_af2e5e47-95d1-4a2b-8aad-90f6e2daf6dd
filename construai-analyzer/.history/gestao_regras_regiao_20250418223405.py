from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Union
import yaml
import os
import json

# Modelos de dados
class OccupancyRule(BaseModel):
    max_percentage: float
    zone_exceptions: Dict[str, float] = Field(default_factory=dict)
    building_type_adjustments: Dict[str, float] = Field(default_factory=dict)

class ZoningRule(BaseModel):
    allowed_uses: List[str]
    max_height: Optional[float] = None
    min_setback: Dict[str, float]
    max_floors: Optional[int] = None

class StructuralRule(BaseModel):
    safety_factor: float
    load_calculations: Dict[str, str]
    material_requirements: Dict[str, Dict[str, Union[float, str]]]

class RegionRules(BaseModel):
    country: str
    region: str
    city: Optional[str] = None
    effective_date: str
    code_version: str
    occupancy: OccupancyRule
    zoning: Dict[str, ZoningRule]
    structural: StructuralRule
    additional_requirements: Dict[str, Dict] = Field(default_factory=dict)

# Sistema de gerenciamento de regras
class RuleManager:
    def __init__(self, rules_dir: str = "rules"):
        self.rules_dir = rules_dir
        self.rules_cache = {}
        
        # Criar diretório se não existir
        os.makedirs(rules_dir, exist_ok=True)
        
        # Carregar regras existentes
        self._load_rules()
    
    def _load_rules(self):
        """Carrega todas as regras dos arquivos YAML."""
        for filename in os.listdir(self.rules_dir):
            if filename.endswith(('.yaml', '.yml')):
                file_path = os.path.join(self.rules_dir, filename)
                with open(file_path, 'r') as file:
                    rule_data = yaml.safe_load(file)
                    
                    # Validar com Pydantic
                    rule = RegionRules(**rule_data)
                    
                    # Adicionar ao cache
                    key = f"{rule.country}:{rule.region}"
                    if rule.city:
                        key += f":{rule.city}"
                    
                    self.rules_cache[key] = rule
    
    def get_rules(self, country: str, region: str, city: Optional[str] = None) -> Optional[RegionRules]:
        """Obtém regras para uma localidade específica."""
        # Tentar obter regras específicas da cidade
        if city:
            key = f"{country}:{region}:{city}"
            if key in self.rules_cache:
                return self.rules_cache[key]
        
        # Tentar obter regras da região
        key = f"{country}:{region}"
        if key in self.rules_cache:
            return self.rules_cache[key]
        
        # Tentar obter regras do país
        key = f"{country}:default"
        if key in self.rules_cache:
            return self.rules_cache[key]
        
        return None
    
    def add_rules(self, rules: RegionRules) -> bool:
        """Adiciona ou atualiza regras para uma localidade."""
        # Criar chave
        key = f"{rules.country}:{rules.region}"
        if rules.city:
            key += f":{rules.city}"
        
        # Adicionar ao cache
        self.rules_cache[key] = rules
        
        # Salvar em arquivo
        filename = key.replace(":", "_") + ".yaml"
        file_path = os.path.join(self.rules_dir, filename)
        
        with open(file_path, 'w') as file:
            yaml.dump(rules.dict(), file)
        
        return True
    
    def apply_rules(self, project_data: dict, country: str, region: str, city: Optional[str] = None) -> dict:
        """Aplica regras a um projeto específico e retorna análise."""
        rules = self.get_rules(country, region, city)
        if not rules:
            raise ValueError(f"Regras não encontradas para {country}/{region}/{city if city else ''}")
        
        # Inicializar resultado
        result = {
            "conformity": True,
            "issues": [],
            "details": {}
        }
        
        # Analisar taxa de ocupação
        try:
            occupancy_rate = project_data.get("occupancy_rate", 0)
            zone_type = project_data.get("zone_type", "")
            building_type = project_data.get("building_type", "")
            
            # Determinar limite aplicável
            limit = rules.occupancy.max_percentage
            
            # Aplicar exceções de zona
            if zone_type in rules.occupancy.zone_exceptions:
                limit = rules.occupancy.zone_exceptions[zone_type]
            
            # Aplicar ajustes de tipo de construção
            if building_type in rules.occupancy.building_type_adjustments:
                limit += rules.occupancy.building_type_adjustments[building_type]
            
            # Verificar conformidade
            if occupancy_rate > limit:
                result["conformity"] = False
                result["issues"].append(f"Taxa de ocupação ({occupancy_rate:.2f}) excede o limite de {limit:.2f}")
            
            result["details"]["occupancy"] = {
                "actual": occupancy_rate,
                "limit": limit,
                "conformity": occupancy_rate <= limit
            }
        except Exception as e:
            result["issues"].append(f"Erro ao analisar taxa de ocupação: {str(e)}")
        
        # Análises de zoneamento e estruturais seguiriam lógica similar
        # ...
        
        return result

# Exemplo de criação de regras para São Paulo
def create_sample_rules():
    sao_paulo_rules = RegionRules(
        country="Brasil",
        region="SP",
        city="São Paulo",
        effective_date="2023-01-01",
        code_version="Lei 16.402/2016 (LPUOS)",
        occupancy=OccupancyRule(
            max_percentage=0.7,
            zone_exceptions={
                "ZEU": 0.85,
                "ZEUP": 0.8,
                "ZM": 0.7,
                "ZEIS": 0.8
            },
            building_type_adjustments={
                "residential_multi": 0.05,
                "commercial_office": 0.0,
                "industrial": -0.1
            }
        ),
        zoning={
            "ZEU": ZoningRule(
                allowed_uses=["residential", "commercial", "mixed", "institutional"],
                max_height=None,  # Sem limite
                min_setback={"front": 5.0, "sides": 2.0, "back": 3.0},
                max_floors=None
            ),
            "ZM": ZoningRule(
                allowed_uses=["residential", "commercial", "mixed", "institutional"],
                max_height=28.0,
                min_setback={"front": 5.0, "sides": 2.0, "back": 3.0},
                max_floors=8
            ),
            # Outras zonas...
        },
        structural=StructuralRule(
            safety_factor=1.5,
            load_calculations={
                "residential": "1.5 kN/m²",
                "commercial": "2.0 kN/m²",
                "industrial": "5.0 kN/m²"
            },
            material_requirements={
                "concrete": {
                    "min_strength": 25.0,
                    "type": "CP-II-Z"
                },
                "steel": {
                    "min_strength": 500.0,
                    "type": "CA-50"
                }
            }
        ),
        additional_requirements={
            "accessibility": {
                "ramp_gradient": 8.33,  # %
                "door_width": 0.8  # metros
            },
            "sustainability": {
                "rainwater_collection": True,
                "solar_panels": False
            }
        }
    )
    
    # Salvar em arquivo
    rule_manager = RuleManager()
    rule_manager.add_rules(sao_paulo_rules)
    
    # Criar regras para EUA (exemplo)
    nyc_rules = RegionRules(
        country="USA",
        region="NY",
        city="New York",
        effective_date="2022-01-01",
        code_version="NYC Building Code 2022",
        occupancy=OccupancyRule(
            max_percentage=0.8,
            zone_exceptions={
                "R1": 0.5,
                "R2": 0.6,
                "C1": 0.9,
                "M1": 0.95
            },
            building_type_adjustments={
                "residential_single": -0.1,
                "residential_multi": 0.0,
                "commercial_retail": 0.05
            }
        ),
        zoning={
            "R1": ZoningRule(
                allowed_uses=["residential_single"],
                max_height=10.7,  # metros (35 pés)
                min_setback={"front": 6.1, "sides": 2.4, "back": 9.1},  # metros
                max_floors=2
            ),
            # Outras zonas...
        },
        structural=StructuralRule(
            safety_factor=2.0,
            load_calculations={
                "residential": "40 psf",
                "commercial": "50 psf",
                "industrial": "125 psf"
            },
            material_requirements={
                "concrete": {
                    "min_strength": 3000,  # psi
                    "type": "Normal weight"
                },
                "steel": {
                    "min_strength": 36000,  # psi
                    "type": "A36"
                }
            }
        ),
        additional_requirements={
            "fire_safety": {
                "sprinklers_required": True,
                "fire_resistance_rating": "2 hour"
            }
        }
    )
    
    rule_manager.add_rules(nyc_rules)
    
    return rule_manager

# Uso do sistema
if __name__ == "__main__":
    # Criar regras de exemplo
    rule_manager = create_sample_rules()
    
    # Exemplo de projeto para análise
    sample_project = {
        "occupancy_rate": 0.75,
        "zone_type": "ZM",
        "building_type": "residential_multi",
        # Outros dados do projeto...
    }
    
    # Analisar conformidade
    result = rule_manager.apply_rules(sample_project, "Brasil", "SP", "São Paulo")
    print(json.dumps(result, indent=2))