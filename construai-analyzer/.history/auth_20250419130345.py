#!/usr/bin/env python3
"""
ConstruAI Analyzer - Sistema de Autenticação
Este módulo fornece funcionalidades de autenticação para o ConstruAI Analyzer.
"""

import os
import json
import hashlib
import logging
import secrets
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timedelta

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("auth.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("auth")

# Configurações
USERS_FILE = "users.json"
SESSION_FILE = "sessions.json"
SESSION_DURATION = 24  # horas

class AuthManager:
    """Gerenciador de autenticação para o ConstruAI Analyzer."""

    def __init__(self):
        """Inicializa o gerenciador de autenticação."""
        self.users = self._load_users()
        self.sessions = self._load_sessions()
        logger.info("Gerenciador de autenticação inicializado")

    def _load_users(self) -> Dict[str, Any]:
        """Carrega os usuários do arquivo."""
        if os.path.exists(USERS_FILE):
            try:
                with open(USERS_FILE, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Erro ao carregar usuários: {str(e)}")
                return {}
        else:
            # Criar arquivo de usuários com admin padrão
            default_users = {
                "admin": {
                    "password_hash": self._hash_password("admin123"),
                    "name": "Administrador",
                    "email": "<EMAIL>",
                    "role": "admin",
                    "created_at": datetime.now().isoformat()
                }
            }
            self._save_users(default_users)
            logger.info("Arquivo de usuários criado com usuário admin padrão")
            return default_users

    def _save_users(self, users: Dict[str, Any]) -> bool:
        """Salva os usuários no arquivo."""
        try:
            with open(USERS_FILE, 'w', encoding='utf-8') as f:
                json.dump(users, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"Erro ao salvar usuários: {str(e)}")
            return False

    def _load_sessions(self) -> Dict[str, Any]:
        """Carrega as sessões do arquivo."""
        if os.path.exists(SESSION_FILE):
            try:
                with open(SESSION_FILE, 'r', encoding='utf-8') as f:
                    sessions = json.load(f)
                    # Limpar sessões expiradas
                    self._clean_expired_sessions(sessions)
                    return sessions
            except Exception as e:
                logger.error(f"Erro ao carregar sessões: {str(e)}")
                return {}
        else:
            return {}

    def _save_sessions(self, sessions: Dict[str, Any]) -> bool:
        """Salva as sessões no arquivo."""
        try:
            with open(SESSION_FILE, 'w', encoding='utf-8') as f:
                json.dump(sessions, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"Erro ao salvar sessões: {str(e)}")
            return False

    def _clean_expired_sessions(self, sessions: Dict[str, Any]) -> Dict[str, Any]:
        """Remove sessões expiradas."""
        now = datetime.now()
        expired_tokens = []

        for token, session in sessions.items():
            expires_at = datetime.fromisoformat(session["expires_at"])
            if expires_at < now:
                expired_tokens.append(token)

        for token in expired_tokens:
            del sessions[token]

        if expired_tokens:
            logger.info(f"Removidas {len(expired_tokens)} sessões expiradas")
            self._save_sessions(sessions)

        return sessions

    def _hash_password(self, password: str) -> str:
        """Gera um hash seguro para a senha."""
        return hashlib.sha256(password.encode()).hexdigest()

    def _generate_session_token(self) -> str:
        """Gera um token de sessão único."""
        return secrets.token_hex(32)

    def _is_email_in_use(self, email: str) -> bool:
        """Verifica se um e-mail já está em uso."""
        for user_data in self.users.values():
            if user_data.get("email", "").lower() == email.lower():
                return True
        return False

    def _validate_password_strength(self, password: str) -> Tuple[bool, str]:
        """Valida a força da senha."""
        if len(password) < 6:
            return False, "A senha deve ter pelo menos 6 caracteres."

        # Verificar se tem pelo menos um número
        if not any(c.isdigit() for c in password):
            return False, "A senha deve conter pelo menos um número."

        return True, ""

    def register_user(self, username: str, password: str, name: str, email: str, role: str = "user") -> Tuple[bool, str]:
        """Registra um novo usuário."""
        # Validar nome de usuário
        if not username or len(username) < 3:
            return False, "O nome de usuário deve ter pelo menos 3 caracteres."

        if username in self.users:
            logger.warning(f"Tentativa de registrar usuário existente: {username}")
            return False, "Nome de usuário já existe."

        # Validar e-mail
        if not email or "@" not in email or "." not in email:
            return False, "E-mail inválido."

        if self._is_email_in_use(email):
            logger.warning(f"Tentativa de registrar e-mail já em uso: {email}")
            return False, "E-mail já está em uso."

        # Validar senha
        is_valid, message = self._validate_password_strength(password)
        if not is_valid:
            return False, message

        # Criar usuário
        self.users[username] = {
            "password_hash": self._hash_password(password),
            "name": name,
            "email": email,
            "role": role,
            "created_at": datetime.now().isoformat()
        }

        success = self._save_users(self.users)
        if success:
            logger.info(f"Usuário registrado com sucesso: {username}")
            return True, "Usuário registrado com sucesso!"
        else:
            return False, "Erro ao salvar usuário. Tente novamente."

    def _find_user_by_email(self, email: str) -> Optional[str]:
        """Encontra um usuário pelo e-mail."""
        for username, user_data in self.users.items():
            if user_data.get("email", "").lower() == email.lower():
                return username
        return None

    def login(self, identifier: str, password: str) -> Tuple[bool, Optional[str]]:
        """Autentica um usuário e retorna um token de sessão.

        O identificador pode ser o nome de usuário ou o e-mail.
        """
        # Verificar se o identificador é um e-mail
        username = identifier
        if "@" in identifier:
            # Buscar usuário pelo e-mail
            username = self._find_user_by_email(identifier)
            if not username:
                logger.warning(f"Tentativa de login com e-mail inexistente: {identifier}")
                return False, None
        elif username not in self.users:
            logger.warning(f"Tentativa de login com usuário inexistente: {identifier}")
            return False, None

        user = self.users[username]
        password_hash = self._hash_password(password)

        if password_hash != user["password_hash"]:
            logger.warning(f"Tentativa de login com senha incorreta: {username}")
            return False, None

        # Gerar token de sessão
        token = self._generate_session_token()
        expires_at = datetime.now() + timedelta(hours=SESSION_DURATION)

        # Salvar sessão
        self.sessions[token] = {
            "username": username,
            "created_at": datetime.now().isoformat(),
            "expires_at": expires_at.isoformat(),
            "user_agent": "Unknown"  # Pode ser atualizado posteriormente
        }

        self._save_sessions(self.sessions)
        logger.info(f"Login bem-sucedido: {username}")
        return True, token

    def logout(self, token: str) -> bool:
        """Encerra uma sessão de usuário."""
        if token in self.sessions:
            username = self.sessions[token]["username"]
            del self.sessions[token]
            self._save_sessions(self.sessions)
            logger.info(f"Logout bem-sucedido: {username}")
            return True
        return False

    def validate_session(self, token: str) -> Tuple[bool, Optional[str]]:
        """Valida um token de sessão e retorna o nome de usuário."""
        if token not in self.sessions:
            return False, None

        session = self.sessions[token]
        expires_at = datetime.fromisoformat(session["expires_at"])

        if expires_at < datetime.now():
            # Sessão expirada
            del self.sessions[token]
            self._save_sessions(self.sessions)
            return False, None

        return True, session["username"]

    def get_user_info(self, username: str) -> Optional[Dict[str, Any]]:
        """Retorna informações do usuário."""
        if username not in self.users:
            return None

        user = self.users[username].copy()
        # Remover informações sensíveis
        if "password_hash" in user:
            del user["password_hash"]
        return user

    def change_password(self, username: str, current_password: str, new_password: str) -> bool:
        """Altera a senha de um usuário."""
        if username not in self.users:
            logger.warning(f"Tentativa de alterar senha de usuário inexistente: {username}")
            return False

        user = self.users[username]
        current_hash = self._hash_password(current_password)

        if current_hash != user["password_hash"]:
            logger.warning(f"Tentativa de alterar senha com senha atual incorreta: {username}")
            return False

        user["password_hash"] = self._hash_password(new_password)
        user["updated_at"] = datetime.now().isoformat()

        success = self._save_users(self.users)
        if success:
            logger.info(f"Senha alterada com sucesso: {username}")
        return success

# Função para testar o sistema de autenticação
def test_auth():
    """Testa o sistema de autenticação."""
    auth = AuthManager()

    # Registrar um novo usuário
    success = auth.register_user(
        username="teste",
        password="senha123",
        name="Usuário de Teste",
        email="<EMAIL>"
    )
    print(f"Registro de usuário: {'Sucesso' if success else 'Falha'}")

    # Fazer login
    success, token = auth.login("teste", "senha123")
    print(f"Login: {'Sucesso' if success else 'Falha'}")
    print(f"Token: {token}")

    # Validar sessão
    if token:
        valid, username = auth.validate_session(token)
        print(f"Validação de sessão: {'Válida' if valid else 'Inválida'}")
        print(f"Usuário: {username}")

        # Obter informações do usuário
        user_info = auth.get_user_info(username)
        print(f"Informações do usuário: {user_info}")

        # Fazer logout
        success = auth.logout(token)
        print(f"Logout: {'Sucesso' if success else 'Falha'}")

        # Validar sessão novamente (deve falhar)
        valid, username = auth.validate_session(token)
        print(f"Validação de sessão após logout: {'Válida' if valid else 'Inválida'}")

if __name__ == "__main__":
    test_auth()
