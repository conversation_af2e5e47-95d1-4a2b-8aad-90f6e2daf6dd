# ConstruAI Analyzer

Análise automatizada de projetos arquitetônicos usando Inteligência Artificial.

## Visão Geral

O ConstruAI Analyzer é uma solução SaaS vertical que utiliza IA para analisar projetos arquitetônicos e identificar:

- Violações de taxa de ocupação
- Incompatibilidades com o zoneamento urbano
- Erros em cálculos estruturais

## Funcionalidades

- **Coleta Automatizada**: Coleta de planos de obras aprovadas de portais públicos
- **Processamento Inteligente**: Análise de documentos PDF usando LlamaIndex e LangChain
- **Interface Amigável**: Chatbot para interação com os resultados da análise
- **Relatórios Detalhados**: Identificação clara de problemas e sugestões de correção

## Requisitos

- Python 3.8+
- OpenAI API Key
- Dependências listadas em `requirements.txt`

## Instalação

1. Clone o repositório:
```bash
git clone https://github.com/seu-usuario/construai-analyzer.git
cd construai-analyzer
```

2. Crie e ative um ambiente virtual:
```bash
python -m venv venv
source venv/bin/activate  # No Windows: venv\Scripts\activate
```

3. Instale as dependências:
```bash
pip install -r requirements.txt
```

4. Configure as variáveis de ambiente:
```bash
cp .env.example .env
# Edite o arquivo .env com suas credenciais
```

## Uso

### Coleta de Dados

Para coletar projetos arquitetônicos de fontes públicas:

```bash
python main.py --collect
```

### Processamento de Documentos

Para processar os documentos coletados e realizar análises:

```bash
python main.py --process
```

### Interface de Chatbot

Para iniciar a interface de chatbot:

```bash
python main.py --chatbot
```

### Pipeline Completo

Para executar todo o pipeline (coleta, processamento e chatbot):

```bash
python main.py --all
```

## Estrutura do Projeto

```
construai-analyzer/
├── coletadados.py           # Coleta de dados de fontes públicas
├── processamento_llama_langchain.py  # Processamento de documentos
├── chatbot_langchain_enhanced.py     # Interface de chatbot
├── main.py                  # Script principal
├── requirements.txt         # Dependências
├── .env                     # Variáveis de ambiente
├── projetos_data/           # Dados coletados
├── embeddings_db/           # Banco de embeddings
└── analises/                # Resultados das análises
```

## Modelo de Negócio

- **Plano Básico**: R$ 299/projeto analisado
- **Plano Premium**: R$ 1.999/mês para consultorias ilimitadas
- **Plano Corporativo**: Preços personalizados para construtoras

## Roadmap

### Mês 1-3: MVP Focado em SP
- Coletar 5.000 planos de obras aprovadas no Portal da Prefeitura de SP
- Treinar modelo de IA para detectar violações e incompatibilidades
- Interface via chatbot

### Mês 4-6: Parcerias Estratégicas
- Oferecer análise gratuita para 50 escritórios de arquitetura
- Integração com softwares populares do setor (AutoCAD via API)

### Mês 7-12: Monetização Agressiva
- Implementação do modelo de preços
- Foco em pequenas construtoras

### Mês 13-18: Escala Global
- Adaptar modelos para normas internacionais
- Parceria com seguradoras para descontos em apólices

## Licença

Este projeto está licenciado sob a licença MIT - veja o arquivo LICENSE para detalhes.

## Contato

Para mais informações, entre em contato pelo email: <EMAIL>
