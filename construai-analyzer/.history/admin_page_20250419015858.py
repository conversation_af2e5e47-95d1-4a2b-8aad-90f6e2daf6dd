#!/usr/bin/env python3
"""
Página de administração para o ConstruAI Analyzer
"""

import streamlit as st
import pandas as pd
import logging
from auth_manager import auth_manager

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("admin_page")

def show_admin_page():
    """Exibe a página de administração."""
    st.title("ConstruAI Analyzer - Administração")

    # Verificar se o usuário é administrador
    if "user" not in st.session_state or st.session_state.user["role"] != "admin":
        st.error("Acesso negado. Você não tem permissão para acessar esta página.")
        return

    # Criar abas para diferentes funcionalidades
    tab1, tab2, tab3 = st.tabs(["Usuários", "Planos", "Estatísticas"])

    with tab1:
        st.header("Gerenciamento de Usuários")

        # Obter todos os usuários
        users = auth_manager.get_all_users()

        # Criar DataFrame para exibição
        df = pd.DataFrame(users)

        # Reordenar colunas
        if not df.empty and "email" in df.columns:
            cols = ["email", "name", "role", "plan", "credits", "created_at", "last_login"]
            df = df[cols]

        # Exibir tabela de usuários
        st.dataframe(df)

        # Adicionar novo usuário
        st.subheader("Adicionar Novo Usuário")
        with st.form("add_user_form"):
            name = st.text_input("Nome")
            email = st.text_input("Email")
            password = st.text_input("Senha", type="password")
            role = st.selectbox("Função", ["user", "admin"])
            plan = st.selectbox("Plano", list(auth_manager.get_plans().keys()))
            submit_button = st.form_submit_button("Adicionar Usuário")

            if submit_button:
                if not name or not email or not password:
                    st.error("Por favor, preencha todos os campos.")
                elif auth_manager.register_user(email, name, password, role, plan):
                    st.success(f"Usuário {email} adicionado com sucesso!")
                    st.rerun()
                else:
                    st.error("Erro ao adicionar usuário. Este email já pode estar em uso.")

        # Gerenciar créditos
        st.subheader("Gerenciar Créditos")
        with st.form("manage_credits_form"):
            user_email = st.selectbox("Usuário", [user["email"] for user in users])
            credits = st.number_input("Créditos a adicionar", min_value=1, value=10)
            submit_button = st.form_submit_button("Adicionar Créditos")

            if submit_button:
                if auth_manager.add_credits(user_email, credits):
                    st.success(f"{credits} créditos adicionados para {user_email}!")
                    st.rerun()
                else:
                    st.error("Erro ao adicionar créditos.")

        # Remover usuário
        st.subheader("Remover Usuário")
        with st.form("remove_user_form"):
            user_email = st.selectbox("Usuário", [user["email"] for user in users], key="remove_user")
            confirm = st.checkbox("Confirmar remoção")
            submit_button = st.form_submit_button("Remover Usuário")

            if submit_button:
                if not confirm:
                    st.error("Por favor, confirme a remoção.")
                elif auth_manager.delete_user(user_email):
                    st.success(f"Usuário {user_email} removido com sucesso!")
                    st.rerun()
                else:
                    st.error("Erro ao remover usuário.")

    with tab2:
        st.header("Planos Disponíveis")

        # Obter planos
        plans = auth_manager.get_plans()

        # Exibir planos
        for plan_id, plan_data in plans.items():
            st.subheader(f"{plan_data['name']} ({plan_id})")
            st.write(f"Preço: R$ {plan_data['price']:.2f}")
            st.write(f"Créditos: {plan_data['credits']}")
            st.write("Recursos:")
            for feature in plan_data['features']:
                st.write(f"- {feature}")
            st.markdown("---")

    with tab3:
        st.header("Estatísticas")

        # Calcular estatísticas básicas
        total_users = len(users)
        active_users = sum(1 for user in users if user.get("last_login") is not None)
        total_credits = sum(user.get("credits", 0) for user in users)

        # Exibir estatísticas
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Total de Usuários", total_users)
        with col2:
            st.metric("Usuários Ativos", active_users)
        with col3:
            st.metric("Total de Créditos", total_credits)

        # Distribuição de planos
        plan_counts = {}
        for user in users:
            plan = user.get("plan", "free")
            plan_counts[plan] = plan_counts.get(plan, 0) + 1

        # Criar DataFrame para gráfico
        plan_df = pd.DataFrame({
            "Plano": list(plan_counts.keys()),
            "Usuários": list(plan_counts.values())
        })

        # Exibir gráfico
        st.subheader("Distribuição de Planos")
        st.bar_chart(plan_df.set_index("Plano"))

if __name__ == "__main__":
    # Configurar página
    st.set_page_config(
        page_title="ConstruAI Analyzer - Admin",
        page_icon="🏗️",
        layout="wide"
    )

    # Exibir página de administração
    show_admin_page()
