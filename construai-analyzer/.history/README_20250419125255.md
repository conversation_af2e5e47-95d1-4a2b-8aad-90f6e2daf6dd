# ConstruAI Analyzer

Analisador automatizado de projetos arquitetônicos usando IA.

## Visão Geral

O ConstruAI Analyzer é uma plataforma SaaS que utiliza Inteligência Artificial para analisar projetos arquitetônicos e verificar sua conformidade com normas e regulamentos de construção. A plataforma extrai informações relevantes de projetos em PDF, como áreas, dimensões e especificações técnicas, e verifica automaticamente a conformidade com normas municipais.

## Funcionalidades Principais

- **Análise de Conformidade**: Verificação automática de conformidade com normas e regulamentos
- **Relatórios Detalhados**: Geração de relatórios em PDF com visualizações e recomendações
- **Interface Web**: Interface amigável para upload e análise de projetos
- **Autenticação de Usuários**: Sistema de login e registro de usuários
- **Coleta de Dados**: Coleta automática de dados de fontes públicas

## Requisitos

- Python 3.8+
- Google Gemini API Key
- Dependências listadas em `requirements.txt`

## Instalação

1. Clone o repositório:

```bash
git clone https://github.com/seu-usuario/construai-analyzer.git
cd construai-analyzer
```

2. Crie e ative um ambiente virtual:

```bash
python -m venv venv
source venv/bin/activate  # No Windows: venv\Scripts\activate
```

3. Instale as dependências:

```bash
pip install -r requirements.txt
```

4. Configure as variáveis de ambiente:

```bash
cp .env.example .env
# Edite o arquivo .env com suas credenciais
```

## Uso

### Coleta de Dados

Para coletar projetos arquitetônicos de fontes públicas:

```bash
python main.py --collect
```

### Processamento de Documentos

Para processar os documentos coletados e realizar análises:

```bash
python main.py --process
```

### Demonstração Completa

Para executar a demonstração completa do sistema:

```bash
python demo.py
```

### Interface Web com Autenticação

Para iniciar a interface web com sistema de autenticação:

```bash
python main.py --chatbot
```

### Pipeline Completo

Para executar todo o pipeline (coleta, processamento e chatbot):

```bash
python main.py --all
```

### Landing Page

Para iniciar a landing page do produto:

```bash
streamlit run landing_page.py
```

## Estrutura do Projeto

```
construai-analyzer/
├── main.py                  # Script principal
├── demo.py                  # Script de demonstração
├── pdf_analyzer.py          # Análise de PDFs com IA
├── report_generator.py      # Geração de relatórios
├── download_geosampa.py     # Coleta de dados do GeoSampa
├── coletadados.py           # Coleta de dados de fontes públicas
├── auth.py                  # Sistema de autenticação
├── chatbot_auth.py          # Interface web com autenticação
├── landing_page.py          # Landing page do produto
├── requirements.txt         # Dependências
├── .env                     # Variáveis de ambiente
├── projetos_data/           # Dados coletados
├── analises/                # Resultados das análises
└── temp_charts/             # Gráficos temporários
```

## Modelo de Negócio

- **Plano Básico**: R$ 299/projeto analisado
- **Plano Premium**: R$ 1.999/mês para consultorias ilimitadas
- **Plano Corporativo**: Preços personalizados para construtoras

## Roadmap

- [x] Coleta de dados de fontes públicas
- [x] Processamento básico de documentos
- [x] Interface web com autenticação
- [x] Geração de relatórios em PDF
- [x] Landing page do produto
- [ ] Integração com sistemas CAD
- [ ] Detecção avançada de problemas estruturais
- [ ] Suporte a mais cidades e regulamentos
- [ ] Dashboard de análises para empresas

## Contribuição

Contribuições são bem-vindas! Por favor, sinta-se à vontade para enviar pull requests ou abrir issues para melhorar o projeto.

## Licença

Este projeto está licenciado sob a licença MIT - veja o arquivo LICENSE para detalhes.

## Contato

Para mais informações, entre em contato pelo email: <EMAIL>
