#!/usr/bin/env python3
"""
ConstruAI Analyzer - Gerador de Relatórios
Este módulo gera relatórios em PDF a partir das análises de projetos.
"""

import os
import time
import logging
from datetime import datetime
from typing import Dict, Any
from fpdf import FPDF
import matplotlib.pyplot as plt
import numpy as np

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("report_generator.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("report_generator")

class ReportPDF(FPDF):
    """Classe personalizada para gerar relatórios em PDF."""
    
    def __init__(self):
        super().__init__()
        self.set_auto_page_break(auto=True, margin=15)
        self.add_font('DejaVu', '', 'DejaVuSansCondensed.ttf', uni=True)
        self.add_font('DejaVu', 'B', 'DejaVuSansCondensed-Bold.ttf', uni=True)
        
    def header(self):
        # Logo
        try:
            self.image('logo.png', 10, 8, 33)
        except:
            pass
        # Título
        self.set_font('Arial', 'B', 15)
        self.cell(0, 10, 'ConstruAI Analyzer - Relatório de Análise', 0, 1, 'C')
        # Data
        self.set_font('Arial', 'I', 8)
        self.cell(0, 5, f'Gerado em: {datetime.now().strftime("%d/%m/%Y %H:%M:%S")}', 0, 1, 'R')
        # Linha
        self.line(10, 25, 200, 25)
        self.ln(10)
        
    def footer(self):
        # Posição a 1.5 cm do final
        self.set_y(-15)
        # Fonte
        self.set_font('Arial', 'I', 8)
        # Número da página
        self.cell(0, 10, f'Página {self.page_no()}/{{nb}}', 0, 0, 'C')
        # Copyright
        self.cell(0, 10, 'ConstruAI Analyzer © 2023', 0, 0, 'R')
        
    def chapter_title(self, title):
        self.set_font('Arial', 'B', 12)
        self.set_fill_color(200, 220, 255)
        self.cell(0, 6, title, 0, 1, 'L', 1)
        self.ln(4)
        
    def chapter_body(self, body):
        self.set_font('Arial', '', 11)
        self.multi_cell(0, 5, body)
        self.ln()
        
    def add_table(self, headers, data):
        # Largura das colunas
        col_width = self.w / len(headers) - 10
        # Altura da linha
        row_height = 6
        
        # Cabeçalho
        self.set_font('Arial', 'B', 10)
        self.set_fill_color(200, 220, 255)
        for header in headers:
            self.cell(col_width, row_height, header, 1, 0, 'C', 1)
        self.ln()
        
        # Dados
        self.set_font('Arial', '', 10)
        self.set_fill_color(255, 255, 255)
        for row in data:
            for item in row:
                self.cell(col_width, row_height, str(item), 1, 0, 'C', 0)
            self.ln()
        self.ln(5)
        
    def add_image(self, image_path, caption=""):
        if os.path.exists(image_path):
            self.image(image_path, x=None, y=None, w=180)
            if caption:
                self.set_font('Arial', 'I', 9)
                self.cell(0, 5, caption, 0, 1, 'C')
            self.ln(5)

def create_compliance_chart(project_data, output_path="compliance_chart.png"):
    """Cria um gráfico de conformidade do projeto."""
    try:
        # Dados para o gráfico
        taxa_ocupacao = project_data.get("taxa_ocupacao", 0)
        taxa_max = 70  # Limite máximo permitido
        
        # Criar figura
        plt.figure(figsize=(10, 6))
        
        # Gráfico de barras para taxa de ocupação
        plt.subplot(1, 2, 1)
        bars = plt.bar(['Projeto', 'Limite Máximo'], [taxa_ocupacao, taxa_max], color=['#3498db', '#e74c3c'])
        plt.ylim(0, max(taxa_ocupacao, taxa_max) * 1.2)
        plt.title('Taxa de Ocupação (%)')
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        
        # Adicionar valores nas barras
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{height}%', ha='center', va='bottom')
        
        # Gráfico de pizza para conformidade
        plt.subplot(1, 2, 2)
        conformidade = project_data.get("coeficiente_aproveitamento", 0)
        max_conformidade = 4.0  # Valor máximo permitido
        
        # Calcular percentual de uso
        percentual_uso = min(100, (conformidade / max_conformidade) * 100)
        
        # Criar gráfico de pizza
        plt.pie([percentual_uso, 100 - percentual_uso], 
                labels=[f'Utilizado\n({conformidade})', f'Disponível\n({max_conformidade - conformidade:.1f})'],
                colors=['#3498db', '#ecf0f1'],
                autopct='%1.1f%%',
                startangle=90,
                wedgeprops={'edgecolor': 'black', 'linewidth': 1})
        plt.title('Coeficiente de Aproveitamento')
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=100, bbox_inches='tight')
        plt.close()
        
        return output_path
    except Exception as e:
        logger.error(f"Erro ao criar gráfico: {str(e)}")
        return None

def generate_report(analysis_result: Dict[str, Any], output_path: str) -> bool:
    """Gera um relatório em PDF a partir dos resultados da análise."""
    try:
        logger.info(f"Gerando relatório para: {output_path}")
        
        # Verificar se temos dados do projeto
        if "project_data" not in analysis_result or not analysis_result["project_data"]:
            logger.error("Dados do projeto não encontrados no resultado da análise")
            return False
        
        project_data = analysis_result["project_data"]
        compliance = analysis_result.get("compliance", {})
        
        # Criar diretório temporário para gráficos
        temp_dir = "temp_charts"
        os.makedirs(temp_dir, exist_ok=True)
        
        # Criar gráfico de conformidade
        chart_path = create_compliance_chart(project_data, os.path.join(temp_dir, "compliance_chart.png"))
        
        # Inicializar PDF
        pdf = ReportPDF()
        pdf.alias_nb_pages()
        pdf.add_page()
        
        # Informações do Projeto
        pdf.chapter_title("1. Informações do Projeto")
        
        # Tabela com dados básicos
        headers = ["Parâmetro", "Valor", "Unidade"]
        data = [
            ["Área do Terreno", project_data.get("area_terreno", "N/A"), "m²"],
            ["Área Construída", project_data.get("area_construida", "N/A"), "m²"],
            ["Taxa de Ocupação", project_data.get("taxa_ocupacao", "N/A"), "%"],
            ["Coef. Aproveitamento", project_data.get("coeficiente_aproveitamento", "N/A"), "-"],
            ["Zoneamento", project_data.get("zoneamento", "N/A"), "-"],
            ["Tipo de Construção", project_data.get("tipo_construcao", "N/A"), "-"]
        ]
        
        # Ajustar largura das colunas
        col_width = pdf.w / 3 - 10
        row_height = 6
        
        # Cabeçalho
        pdf.set_font('Arial', 'B', 10)
        pdf.set_fill_color(200, 220, 255)
        for i, header in enumerate(headers):
            width = col_width * 2 if i == 0 else col_width
            pdf.cell(width, row_height, header, 1, 0, 'C', 1)
        pdf.ln()
        
        # Dados
        pdf.set_font('Arial', '', 10)
        pdf.set_fill_color(255, 255, 255)
        for row in data:
            pdf.cell(col_width * 2, row_height, str(row[0]), 1, 0, 'L', 0)
            pdf.cell(col_width, row_height, str(row[1]), 1, 0, 'C', 0)
            pdf.cell(col_width, row_height, str(row[2]), 1, 0, 'C', 0)
            pdf.ln()
        pdf.ln(5)
        
        # Adicionar gráfico
        if chart_path and os.path.exists(chart_path):
            pdf.add_image(chart_path, "Figura 1: Análise de Conformidade do Projeto")
        
        # Análise de Conformidade
        pdf.add_page()
        pdf.chapter_title("2. Análise de Conformidade")
        
        # Status geral
        em_conformidade = compliance.get("em_conformidade", False)
        status_text = "✓ O projeto está em conformidade com as normas." if em_conformidade else "✗ O projeto apresenta problemas de conformidade."
        pdf.set_font('Arial', 'B', 12)
        pdf.set_text_color(0, 128, 0) if em_conformidade else pdf.set_text_color(255, 0, 0)
        pdf.cell(0, 10, status_text, 0, 1, 'L')
        pdf.set_text_color(0, 0, 0)
        pdf.ln(5)
        
        # Problemas identificados
        if not em_conformidade:
            problemas = compliance.get("problemas_identificados", [])
            if problemas:
                pdf.set_font('Arial', 'B', 11)
                pdf.cell(0, 6, "Problemas Identificados:", 0, 1, 'L')
                pdf.set_font('Arial', '', 11)
                for problema in problemas:
                    pdf.cell(10, 6, "•", 0, 0, 'R')
                    pdf.cell(0, 6, problema, 0, 1, 'L')
                pdf.ln(5)
        
        # Detalhes das análises
        if "analises" in compliance:
            analises = compliance["analises"]
            
            # Taxa de Ocupação
            if "taxa_ocupacao" in analises:
                pdf.set_font('Arial', 'B', 11)
                pdf.cell(0, 6, "Taxa de Ocupação:", 0, 1, 'L')
                pdf.set_font('Arial', '', 11)
                taxa_info = analises["taxa_ocupacao"]
                pdf.multi_cell(0, 5, taxa_info.get("mensagem", "Não disponível"))
                pdf.ln(5)
            
            # Zoneamento
            if "zoneamento" in analises:
                pdf.set_font('Arial', 'B', 11)
                pdf.cell(0, 6, "Zoneamento:", 0, 1, 'L')
                pdf.set_font('Arial', '', 11)
                zona_info = analises["zoneamento"]
                pdf.multi_cell(0, 5, zona_info.get("mensagem", "Não disponível"))
                pdf.ln(5)
            
            # Estrutura
            if "estrutura" in analises:
                pdf.set_font('Arial', 'B', 11)
                pdf.cell(0, 6, "Análise Estrutural:", 0, 1, 'L')
                pdf.set_font('Arial', '', 11)
                estrutura_info = analises["estrutura"]
                pdf.multi_cell(0, 5, estrutura_info.get("mensagem", "Não disponível"))
                pdf.ln(5)
        
        # Especificações Estruturais
        pdf.add_page()
        pdf.chapter_title("3. Especificações Estruturais")
        
        especificacoes = project_data.get("especificacoes_estruturais", {})
        
        # Vigas
        pdf.set_font('Arial', 'B', 11)
        pdf.cell(0, 6, "Vigas:", 0, 1, 'L')
        pdf.set_font('Arial', '', 11)
        pdf.multi_cell(0, 5, especificacoes.get("vigas", "Não disponível"))
        pdf.ln(5)
        
        # Pilares
        pdf.set_font('Arial', 'B', 11)
        pdf.cell(0, 6, "Pilares:", 0, 1, 'L')
        pdf.set_font('Arial', '', 11)
        pdf.multi_cell(0, 5, especificacoes.get("pilares", "Não disponível"))
        pdf.ln(5)
        
        # Fundação
        pdf.set_font('Arial', 'B', 11)
        pdf.cell(0, 6, "Fundação:", 0, 1, 'L')
        pdf.set_font('Arial', '', 11)
        pdf.multi_cell(0, 5, especificacoes.get("fundacao", "Não disponível"))
        pdf.ln(5)
        
        # Recomendações
        pdf.add_page()
        pdf.chapter_title("4. Recomendações")
        
        if em_conformidade:
            pdf.chapter_body("O projeto está em conformidade com as normas e regulamentos aplicáveis. Recomendamos prosseguir com o processo de aprovação junto aos órgãos competentes.")
        else:
            pdf.chapter_body("Com base na análise realizada, recomendamos as seguintes ações para adequar o projeto às normas e regulamentos aplicáveis:")
            
            pdf.ln(5)
            pdf.set_font('Arial', '', 11)
            
            if "taxa_ocupacao" in analises and not analises["taxa_ocupacao"].get("conforme", True):
                pdf.cell(10, 6, "•", 0, 0, 'R')
                pdf.cell(0, 6, "Reduzir a área construída para atender ao limite de taxa de ocupação de 70%.", 0, 1, 'L')
            
            if "zoneamento" in analises and not analises["zoneamento"].get("conforme", True):
                pdf.cell(10, 6, "•", 0, 0, 'R')
                pdf.cell(0, 6, "Verificar a compatibilidade do tipo de construção com o zoneamento da área.", 0, 1, 'L')
            
            if "estrutura" in analises and not analises["estrutura"].get("conforme", True):
                pdf.cell(10, 6, "•", 0, 0, 'R')
                pdf.cell(0, 6, "Revisar os cálculos estruturais para garantir a segurança da edificação.", 0, 1, 'L')
        
        # Salvar PDF
        pdf.output(output_path)
        
        # Limpar arquivos temporários
        if chart_path and os.path.exists(chart_path):
            os.remove(chart_path)
        
        logger.info(f"Relatório gerado com sucesso: {output_path}")
        return True
        
    except Exception as e:
        logger.error(f"Erro ao gerar relatório: {str(e)}")
        return False

if __name__ == "__main__":
    import sys
    import json
    
    if len(sys.argv) < 3:
        print("Uso: python report_generator.py <arquivo_analise.json> <arquivo_saida.pdf>")
        sys.exit(1)
    
    analysis_file = sys.argv[1]
    output_file = sys.argv[2]
    
    try:
        with open(analysis_file, 'r', encoding='utf-8') as f:
            analysis_result = json.load(f)
        
        success = generate_report(analysis_result, output_file)
        if success:
            print(f"Relatório gerado com sucesso: {output_file}")
        else:
            print("Falha ao gerar relatório.")
            
    except Exception as e:
        print(f"Erro: {str(e)}")
        sys.exit(1)
