import os
import argparse
import logging
from dotenv import load_dotenv

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("construai.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("main")

# Carregar variáveis de ambiente
load_dotenv()

def setup_environment():
    """Configura o ambiente, verificando diretórios e dependências."""
    logger.info("Verificando ambiente...")

    # Verificar diretórios necessários
    directories = [
        "projetos_data",
        "projetos_data/pdfs",
        "embeddings_db",
        "analises"
    ]

    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            logger.info(f"Diretório criado: {directory}")

    # Verificar variáveis de ambiente
    required_env_vars = ["OPENAI_API_KEY"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]

    if missing_vars:
        logger.error(f"Variáveis de ambiente ausentes: {', '.join(missing_vars)}")
        logger.error("Por favor, configure o arquivo .env com as variáveis necessárias.")
        return False

    logger.info("Ambiente configurado com sucesso!")
    return True

def run_data_collection():
    """Executa o módulo de coleta de dados."""
    logger.info("Iniciando coleta de dados...")
    try:
        # Primeiro tentamos o download do GeoSampa
        from download_geosampa import main as geosampa_main
        success_count = geosampa_main()
        logger.info(f"Coleta de dados do GeoSampa concluída. {success_count} documentos baixados.")

        # Se quiser tentar o método alternativo também, descomente as linhas abaixo
        # from coletadados import main as coletadados_main
        # alt_success_count = coletadados_main()
        # logger.info(f"Coleta de dados alternativa concluída. {alt_success_count} documentos baixados.")

        return True
    except Exception as e:
        logger.error(f"Erro na coleta de dados: {str(e)}")
        return False

def run_processing():
    """Executa o módulo de processamento de documentos."""
    logger.info("Iniciando processamento de documentos...")
    try:
        # Usar o processamento simplificado que não depende de llama-index
        from processamento_simplificado import processar_documentos
        resultados = processar_documentos()
        logger.info(f"Processamento concluído. {len(resultados)} documentos processados.")
        return True
    except Exception as e:
        logger.error(f"Erro no processamento: {str(e)}")
        return False

def run_chatbot():
    """Executa o chatbot Streamlit."""
    logger.info("Iniciando chatbot...")
    try:
        # Verificar se o chatbot completo existe
        if os.path.exists("chatbot_complete.py"):
            logger.info("Usando chatbot completo com autenticação e conversa com PDFs")
            os.system("streamlit run chatbot_complete.py")
        elif os.path.exists("chatbot_auth.py"):
            logger.info("Usando chatbot com autenticação")
            os.system("streamlit run chatbot_auth.py")
        else:
            logger.info("Usando chatbot sem autenticação")
            os.system("streamlit run chatbot_langchain_enhanced.py")
        return True
    except Exception as e:
        logger.error(f"Erro ao iniciar chatbot: {str(e)}")
        return False

def main():
    """Função principal que coordena a execução dos módulos."""
    parser = argparse.ArgumentParser(description="ConstruAI Analyzer - Análise automatizada de projetos arquitetônicos")
    parser.add_argument("--collect", action="store_true", help="Executar coleta de dados")
    parser.add_argument("--process", action="store_true", help="Executar processamento de documentos")
    parser.add_argument("--chatbot", action="store_true", help="Iniciar chatbot")
    parser.add_argument("--all", action="store_true", help="Executar todo o pipeline")

    args = parser.parse_args()

    # Se nenhum argumento for fornecido, mostrar ajuda
    if not (args.collect or args.process or args.chatbot or args.all):
        parser.print_help()
        return

    # Configurar ambiente
    if not setup_environment():
        return

    # Executar módulos conforme solicitado
    if args.collect or args.all:
        run_data_collection()

    if args.process or args.all:
        run_processing()

    if args.chatbot or args.all:
        run_chatbot()

if __name__ == "__main__":
    main()
