#!/usr/bin/env python3
"""
ConstruAI Analyzer - Aplicação principal com autenticação
"""

import streamlit as st
import os
import logging
from dotenv import load_dotenv

# Configurar página - deve ser a primeira chamada do Streamlit
st.set_page_config(
    page_title="ConstruAI Analyzer",
    page_icon="🏗️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Importar sistema de autenticação
from login_page import show_login_page, show_user_info, init_session
from auth_manager import auth_manager

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("main.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("auth_app")

# Carregar variáveis de ambiente
load_dotenv()

def check_environment():
    """Verifica se o ambiente está configurado corretamente."""
    logger.info("Verificando ambiente...")
    
    # Verificar se as variáveis de ambiente necessárias estão definidas
    if not os.getenv("GOOGLE_API_KEY"):
        logger.warning("GOOGLE_API_KEY não encontrada no arquivo .env")
        return False
    
    logger.info("Ambiente configurado com sucesso!")
    return True

def main():
    """Função principal."""
    # Verificar ambiente
    if not check_environment():
        st.error("Erro ao configurar o ambiente. Verifique o arquivo .env.")
        return
    
    # Inicializar sessão de autenticação
    init_session()
    
    # Verificar se o usuário está autenticado
    if not st.session_state.authenticated:
        show_login_page()
        return
    
    # Mostrar informações do usuário na barra lateral
    show_user_info()
    
    # Adicionar informações de preço na barra lateral
    st.sidebar.title("Seu Plano")
    plan_name = auth_manager.get_plans()[st.session_state.user["plan"]]["name"]
    st.sidebar.info(f"""
    ### Plano atual: {plan_name}
    Créditos restantes: {st.session_state.user["credits"]}
    
    Para mais informações sobre planos, entre em contato.
    """)
    
    st.sidebar.title("Sobre")
    st.sidebar.info("""
    ### ConstruAI Analyzer
    Análise automatizada de projetos arquitetônicos usando IA.
    
    Detectamos:
    - Violações de taxa de ocupação
    - Incompatibilidades com zoneamento
    - Erros em cálculos estruturais
    """)
    
    # Interface principal
    st.title("ConstruAI Analyzer")
    st.markdown("Envie seu projeto em PDF e receba a análise em minutos.")
    
    # Inicializar variáveis de estado da sessão
    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []
    if 'document_processed' not in st.session_state:
        st.session_state.document_processed = False
    if 'analysis_results' not in st.session_state:
        st.session_state.analysis_results = {}
    
    # Importar o analisador de PDF
    from pdf_analyzer import ProjectAnalyzer
    
    # Interface principal
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.subheader("Upload de Projeto")
        uploaded_file = st.file_uploader("Faça upload do seu projeto em PDF", type="pdf")
        
        if uploaded_file and not st.session_state.document_processed:
            # Verificar se o usuário tem créditos suficientes
            if st.session_state.user["credits"] <= 0:
                st.error("Você não tem créditos suficientes para analisar este projeto. Por favor, adquira mais créditos.")
            else:
                # Processar o arquivo
                with st.spinner("Processando seu projeto... Isso pode levar até 3 minutos."):
                    # Importar a função de processamento
                    from chatbot_langchain_enhanced import process_pdf
                    
                    # Processar o arquivo
                    result = process_pdf(uploaded_file)
                    
                    if result:
                        vectorstore, conversation_chain, analysis = result
                        st.session_state.vectorstore = vectorstore
                        st.session_state.conversation_chain = conversation_chain
                        st.session_state.analysis_results = analysis
                        st.session_state.document_processed = True
                        
                        # Usar um crédito
                        auth_manager.use_credit(st.session_state.user["email"])
                        # Atualizar créditos na sessão
                        st.session_state.user["credits"] -= 1
                        
                        st.success("Projeto analisado com sucesso!")
                        st.rerun()
    
    with col2:
        if st.session_state.document_processed:
            st.subheader("Chat com o Projeto")
            
            # Exibir histórico de chat
            for message in st.session_state.chat_history:
                with st.chat_message(message["role"]):
                    st.write(message["content"])
            
            # Input do usuário
            if user_question := st.chat_input("Digite sua pergunta sobre o projeto..."):
                # Verificar se conversation_chain existe
                if not hasattr(st.session_state, 'conversation_chain') or st.session_state.conversation_chain is None:
                    st.error("Erro: Modelo de conversação não inicializado. Por favor, faça upload do projeto novamente.")
                    conversation_ready = False
                else:
                    conversation_ready = True
                    
                # Adicionar pergunta ao histórico
                st.session_state.chat_history.append({"role": "user", "content": user_question})
                
                # Mostrar pergunta
                with st.chat_message("user"):
                    st.write(user_question)
                
                # Gerar resposta apenas se o modelo estiver pronto
                if conversation_ready:
                    with st.chat_message("assistant"):
                        with st.spinner("Analisando..."):
                            try:
                                response = st.session_state.conversation_chain({"question": user_question})
                                st.write(response["answer"])
                                # Adicionar resposta ao histórico
                                st.session_state.chat_history.append({"role": "assistant", "content": response["answer"]})
                            except Exception as e:
                                error_msg = f"Erro ao processar sua pergunta: {str(e)}"
                                st.error(error_msg)
                                # Adicionar erro ao histórico
                                st.session_state.chat_history.append({"role": "assistant", "content": error_msg})
                else:
                    # Adicionar mensagem de erro ao histórico
                    error_msg = "Não foi possível processar sua pergunta. O modelo de conversação não está inicializado."
                    with st.chat_message("assistant"):
                        st.error(error_msg)
                    st.session_state.chat_history.append({"role": "assistant", "content": error_msg})

if __name__ == "__main__":
    main()
