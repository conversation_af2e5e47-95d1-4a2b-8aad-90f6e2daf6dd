#!/usr/bin/env python3
"""
Página de login e registro para o ConstruAI Analyzer
"""

import streamlit as st
import logging
from auth_manager import auth_manager

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("login_page")

def show_login_page():
    """Exibe a página de login."""
    st.title("ConstruAI Analyzer")

    # Criar abas para login e registro
    tab1, tab2 = st.tabs(["Login", "Registro"])

    with tab1:
        st.header("Login")

        # Formulário de login
        with st.form("login_form"):
            email = st.text_input("Email")
            password = st.text_input("Senha", type="password")
            submit_button = st.form_submit_button("Entrar")

            if submit_button:
                if not email or not password:
                    st.error("Por favor, preencha todos os campos.")
                elif auth_manager.authenticate(email, password):
                    # Armazenar dados do usuário na sessão
                    user = auth_manager.get_user(email)
                    st.session_state.user = {
                        "email": email,
                        "name": user["name"],
                        "role": user["role"],
                        "plan": user["plan"],
                        "credits": user["credits"]
                    }
                    st.session_state.authenticated = True
                    st.success("Login realizado com sucesso!")
                    st.rerun()
                else:
                    st.error("Email ou senha incorretos.")

    with tab2:
        st.header("Registro")

        # Formulário de registro
        with st.form("register_form"):
            name = st.text_input("Nome completo")
            email = st.text_input("Email", key="reg_email")
            password = st.text_input("Senha", type="password", key="reg_password")
            password_confirm = st.text_input("Confirmar senha", type="password")

            # Selecionar plano
            plans = auth_manager.get_plans()
            plan_options = {plan_id: f"{plan_data['name']} - R$ {plan_data['price']:.2f}" for plan_id, plan_data in plans.items()}
            selected_plan = st.selectbox("Plano", options=list(plan_options.keys()), format_func=lambda x: plan_options[x])

            # Mostrar detalhes do plano
            st.write(f"**Créditos:** {plans[selected_plan]['credits']}")
            st.write("**Recursos:**")
            for feature in plans[selected_plan]['features']:
                st.write(f"- {feature}")

            submit_button = st.form_submit_button("Registrar")

            if submit_button:
                if not name or not email or not password:
                    st.error("Por favor, preencha todos os campos.")
                elif password != password_confirm:
                    st.error("As senhas não coincidem.")
                elif auth_manager.register_user(email, name, password, plan=selected_plan):
                    st.success("Registro realizado com sucesso! Faça login para continuar.")
                else:
                    st.error("Erro ao registrar usuário. Este email já pode estar em uso.")

def show_logout_button():
    """Exibe o botão de logout."""
    if st.sidebar.button("Sair"):
        # Limpar dados da sessão
        for key in list(st.session_state.keys()):
            del st.session_state[key]
        st.experimental_rerun()

def show_user_info():
    """Exibe informações do usuário."""
    if "user" in st.session_state:
        user = st.session_state.user
        st.sidebar.write(f"**Usuário:** {user['name']}")
        st.sidebar.write(f"**Plano:** {auth_manager.get_plans()[user['plan']]['name']}")
        st.sidebar.write(f"**Créditos:** {user['credits']}")

        # Mostrar botão de logout
        show_logout_button()

def init_session():
    """Inicializa a sessão."""
    if "authenticated" not in st.session_state:
        st.session_state.authenticated = False
    if "user" not in st.session_state:
        st.session_state.user = None
