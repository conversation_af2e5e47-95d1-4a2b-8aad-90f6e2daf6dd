import streamlit as st
import os
import json
import time
import tempfile
import logging
from datetime import datetime
from dotenv import load_dotenv

from langchain.chat_models import ChatOpenAI
from langchain.memory import ConversationBufferMemory
from langchain.chains import ConversationalRetrievalChain
from langchain.document_loaders import PyPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.embeddings import OpenAIEmbeddings
from langchain.vectorstores import FAISS

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("chatbot.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("chatbot")

# Carregar variáveis de ambiente
load_dotenv()

# Obter configurações do arquivo .env
chunk_size = int(os.getenv('CHUNK_SIZE', 1000))
chunk_overlap = int(os.getenv('CHUNK_OVERLAP', 200))

# Configurar página
st.set_page_config(page_title="Analisador de Projetos Arquitetônicos", layout="wide")
st.title("Analisador Automático de Projetos Arquitetônicos")

# Inicializar variáveis de estado da sessão
if 'chat_history' not in st.session_state:
    st.session_state.chat_history = []
if 'document_processed' not in st.session_state:
    st.session_state.document_processed = False
if 'analysis_results' not in st.session_state:
    st.session_state.analysis_results = {}

# Função para processar PDF
def process_pdf(pdf_file):
    with st.spinner("Processando seu projeto... Isso pode levar até 3 minutos."):
        # Salvar PDF temporariamente
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp_file:
            tmp_file.write(pdf_file.read())
            pdf_path = tmp_file.name

        # Carregar e processar PDF
        loader = PyPDFLoader(pdf_path)
        documents = loader.load()

        # Dividir texto em chunks
        text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
        chunks = text_splitter.split_documents(documents)

        # Criar embeddings e banco de vetores
        embeddings = OpenAIEmbeddings()
        vectorstore = FAISS.from_documents(chunks, embeddings)

        # Configurar modelo de chat
        llm = ChatOpenAI(temperature=0)
        memory = ConversationBufferMemory(memory_key="chat_history", return_messages=True)

        # Criar chain de conversação
        conversation_chain = ConversationalRetrievalChain.from_llm(
            llm=llm,
            retriever=vectorstore.as_retriever(),
            memory=memory
        )

        # Simular análise (em produção, substitua por análise real)
        time.sleep(3)  # Simular processamento

        # Realizar análises automáticas
        analysis = {
            "taxa_ocupacao": conversation_chain({"question": "Qual é a taxa de ocupação deste projeto e ela está dentro dos limites permitidos?"})["answer"],
            "zoneamento": conversation_chain({"question": "Este projeto é compatível com o zoneamento da área?"})["answer"],
            "calculos_estruturais": conversation_chain({"question": "Há algum erro nos cálculos estruturais, especialmente na relação entre vigas e cargas?"})["answer"]
        }

        # Limpar arquivo temporário
        os.unlink(pdf_path)

        return vectorstore, conversation_chain, analysis

# Interface principal
col1, col2 = st.columns([1, 2])

with col1:
    st.subheader("Upload de Projeto")
    uploaded_file = st.file_uploader("Faça upload do seu projeto em PDF", type="pdf")

    if uploaded_file and not st.session_state.document_processed:
        vectorstore, conversation_chain, analysis = process_pdf(uploaded_file)
        st.session_state.vectorstore = vectorstore
        st.session_state.conversation_chain = conversation_chain
        st.session_state.analysis_results = analysis
        st.session_state.document_processed = True
        st.success("Projeto analisado com sucesso!")

    if st.session_state.document_processed:
        st.subheader("Resultados da Análise")

        with st.expander("Taxa de Ocupação", expanded=True):
            st.write(st.session_state.analysis_results["taxa_ocupacao"])

        with st.expander("Compatibilidade de Zoneamento"):
            st.write(st.session_state.analysis_results["zoneamento"])

        with st.expander("Análise Estrutural"):
            st.write(st.session_state.analysis_results["calculos_estruturais"])

with col2:
    st.subheader("Pergunte sobre seu projeto")

    # Área de chat
    for message in st.session_state.chat_history:
        with st.chat_message(message["role"]):
            st.write(message["content"])

    # Input do usuário
    if user_question := st.chat_input("Digite sua pergunta sobre o projeto..."):
        if not st.session_state.document_processed:
            st.error("Por favor, faça upload de um projeto primeiro.")
        else:
            # Adicionar pergunta ao histórico
            st.session_state.chat_history.append({"role": "user", "content": user_question})

            # Mostrar pergunta
            with st.chat_message("user"):
                st.write(user_question)

            # Gerar resposta
            with st.chat_message("assistant"):
                with st.spinner("Analisando..."):
                    response = st.session_state.conversation_chain({"question": user_question})
                    st.write(response["answer"])

            # Adicionar resposta ao histórico
            st.session_state.chat_history.append({"role": "assistant", "content": response["answer"]})