from llama_index import SimpleDirectoryReader, VectorStoreIndex, StorageContext
from llama_index.vector_stores import ChromaVectorStore
from llama_index.storage.docstore import SimpleDocumentStore
from llama_index.embeddings import OpenAIEmbedding
from langchain.llms import OpenAI
import chromadb
import os
import json
import logging
import time
from tqdm import tqdm
from dotenv import load_dotenv

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("processamento.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("processamento")

# Carregar variáveis de ambiente
load_dotenv()

# Obter configurações do arquivo .env
chunk_size = int(os.getenv('CHUNK_SIZE', 1000))
chunk_overlap = int(os.getenv('CHUNK_OVERLAP', 200))

# Configurações de diretórios
projetos_data = "projetos_data"
projeto_dir = os.path.join(projetos_data, "pdfs")
db_dir = "embeddings_db"
analysis_output = "analises"

# Criar diretórios necessários
for directory in [projetos_data, projeto_dir, db_dir, analysis_output]:
    os.makedirs(directory, exist_ok=True)
    logger.info(f"Diretório {directory} verificado/criado")

# Configurar cliente Chroma
chroma_client = chromadb.PersistentClient(db_dir)
collection = chroma_client.create_collection("projetos_construcao")

# Configurar armazenamento de documentos
docstore = SimpleDocumentStore()
storage_context = StorageContext.from_defaults(
    docstore=docstore,
    vector_store=ChromaVectorStore(chroma_collection=collection)
)

def carregar_e_indexar_documentos():
    try:
        # Verificar se o diretório de PDFs existe e tem arquivos
        if not os.path.exists(projeto_dir) or len(os.listdir(projeto_dir)) == 0:
            logger.error(f"Diretório {projeto_dir} não existe ou está vazio")
            return None

        # Carregar documentos
        logger.info("Carregando documentos...")
        documents = SimpleDirectoryReader(projeto_dir).load_data()
        logger.info(f"Carregados {len(documents)} documentos")

        if not documents:
            logger.warning("Nenhum documento foi carregado")
            return None

        # Criar índice vetorial
        logger.info("Criando índice de documentos...")
        try:
            index = VectorStoreIndex.from_documents(
                documents,
                storage_context=storage_context,
                embed_model=OpenAIEmbedding()
            )
            logger.info("Índice criado com sucesso")
            return index, documents
        except Exception as e:
            logger.error(f"Erro ao criar índice: {str(e)}")
            return None
    except Exception as e:
        logger.error(f"Erro ao carregar documentos: {str(e)}")
        return None

# Carregar e indexar documentos
resultado = carregar_e_indexar_documentos()
if not resultado:
    logger.error("Falha ao carregar ou indexar documentos. Encerrando.")
    exit(1)

index, documents = resultado

# Motor de consulta
query_engine = index.as_query_engine()

# Funções de análise
def verificar_taxa_ocupacao(doc_id, metadata=None):
    """Verifica se a taxa de ocupação está dentro dos limites permitidos."""
    try:
        prompt = f"""Para o documento {doc_id}, analise cuidadosamente e extraia:
        1. A taxa de ocupação projetada (percentual da área do terreno ocupada pela construção).
        2. Compare com o limite máximo de 70% permitido pela legislação de São Paulo.
        3. Explique detalhadamente se o projeto está em conformidade com a legislação.
        4. Se não estiver em conformidade, indique qual seria o valor máximo permitido para a área.

        Responda de forma estruturada, indicando claramente os valores encontrados e a conclusão."""

        resposta = query_engine.query(prompt)
        logger.info(f"Análise de taxa de ocupação concluída para documento {doc_id}")
        return resposta.response
    except Exception as e:
        logger.error(f"Erro ao verificar taxa de ocupação para {doc_id}: {str(e)}")
        return f"Erro na análise: {str(e)}"

def verificar_zoneamento(doc_id, metadata=None):
    """Verifica se o projeto é compatível com o zoneamento da área."""
    try:
        prompt = f"""Para o documento {doc_id}, analise cuidadosamente e identifique:
        1. O zoneamento da área onde o projeto será construído (residencial, comercial, misto, etc.).
        2. O tipo de construção proposto no projeto.
        3. Verifique se o tipo de construção proposto é permitido no zoneamento identificado.
        4. Cite as regras específicas do zoneamento que se aplicam a este projeto.

        Responda de forma estruturada, indicando claramente o zoneamento, o tipo de construção e a conclusão sobre a compatibilidade."""

        resposta = query_engine.query(prompt)
        logger.info(f"Análise de zoneamento concluída para documento {doc_id}")
        return resposta.response
    except Exception as e:
        logger.error(f"Erro ao verificar zoneamento para {doc_id}: {str(e)}")
        return f"Erro na análise: {str(e)}"

def verificar_calculos_estruturais(doc_id, metadata=None):
    """Verifica se os cálculos estruturais estão corretos."""
    try:
        prompt = f"""Para o documento {doc_id}, analise cuidadosamente os cálculos estruturais:
        1. Identifique as especificações das vigas e pilares (dimensões, materiais, resistência).
        2. Identifique as cargas previstas para a estrutura (cargas permanentes, acidentais, de vento).
        3. Verifique se as dimensões das vigas e pilares são adequadas para suportar as cargas previstas.
        4. Identifique possíveis erros ou inconsistências nos cálculos estruturais.
        5. Sugira correções, se necessário.

        Responda de forma estruturada, indicando claramente os valores encontrados, as inconsistências e a conclusão."""

        resposta = query_engine.query(prompt)
        logger.info(f"Análise estrutural concluída para documento {doc_id}")
        return resposta.response
    except Exception as e:
        logger.error(f"Erro ao verificar cálculos estruturais para {doc_id}: {str(e)}")
        return f"Erro na análise: {str(e)}"

def processar_documentos():
    """Processa todos os documentos e realiza análises."""
    logger.info(f"Iniciando processamento de {len(documents)} documentos")

    resultados = []
    for i, doc in enumerate(tqdm(documents, desc="Analisando documentos")):
        try:
            doc_id = doc.doc_id
            nome_arquivo = doc.metadata.get("file_name", "")

            logger.info(f"Processando documento {i+1}/{len(documents)}: {nome_arquivo}")

            # Realizar análises
            resultado = {
                "doc_id": doc_id,
                "nome_arquivo": nome_arquivo,
                "data_analise": time.strftime("%Y-%m-%d %H:%M:%S"),
                "analises": {
                    "taxa_ocupacao": verificar_taxa_ocupacao(doc_id),
                    "zoneamento": verificar_zoneamento(doc_id),
                    "calculos_estruturais": verificar_calculos_estruturais(doc_id)
                }
            }

            # Adicionar resumo de conformidade
            resultado["resumo"] = {
                "em_conformidade": "taxa de ocupação" not in resultado["analises"]["taxa_ocupacao"].lower() and
                                  "não é permitido" not in resultado["analises"]["zoneamento"].lower() and
                                  "erro" not in resultado["analises"]["calculos_estruturais"].lower(),
                "problemas_identificados": []
            }

            # Identificar problemas
            if "não está em conformidade" in resultado["analises"]["taxa_ocupacao"].lower():
                resultado["resumo"]["problemas_identificados"].append("Taxa de ocupação acima do permitido")

            if "não é permitido" in resultado["analises"]["zoneamento"].lower():
                resultado["resumo"]["problemas_identificados"].append("Incompatibilidade com zoneamento")

            if "erro" in resultado["analises"]["calculos_estruturais"].lower() or "inconsistência" in resultado["analises"]["calculos_estruturais"].lower():
                resultado["resumo"]["problemas_identificados"].append("Problemas nos cálculos estruturais")

            # Salvar resultado individual
            output_file = os.path.join(analysis_output, f"{doc_id}.json")
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(resultado, f, ensure_ascii=False, indent=2)

            logger.info(f"Resultado salvo em {output_file}")
            resultados.append(resultado)

        except Exception as e:
            logger.error(f"Erro ao processar documento {i}: {str(e)}")

    # Salvar resumo de todos os resultados
    resumo_file = os.path.join(analysis_output, "resumo_analises.json")
    with open(resumo_file, 'w', encoding='utf-8') as f:
        json.dump({
            "total_documentos": len(documents),
            "documentos_processados": len(resultados),
            "data_processamento": time.strftime("%Y-%m-%d %H:%M:%S"),
            "resultados": resultados
        }, f, ensure_ascii=False, indent=2)

    logger.info(f"Resumo de análises salvo em {resumo_file}")
    return resultados

# Executar processamento se for o arquivo principal
if __name__ == "__main__":
    processar_documentos()