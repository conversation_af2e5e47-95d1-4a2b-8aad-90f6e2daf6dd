import os
import re
import logging
import json
from typing import Dict, Any
import pypdf
from dotenv import load_dotenv

# Importar Google Gemini
import google.generativeai as genai
from langchain_google_genai import ChatGoogleGenerativeAI

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("pdf_analyzer.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("pdf_analyzer")

# Carregar variáveis de ambiente
load_dotenv()

# Configurar Google Gemini
api_key = os.getenv("GOOGLE_API_KEY")
if api_key:
    genai.configure(api_key=api_key)
    logger.info("Google Gemini API configurada com sucesso")
else:
    logger.warning("Chave da API do Google Gemini não encontrada no arquivo .env")

class ProjectAnalyzer:
    """Classe para analisar projetos arquitetônicos em PDF."""

    def __init__(self):
        """Inicializa o analisador de projetos."""
        # Inicializar o modelo Gemini
        try:
            # O modelo correto para chat é 'gemini-1.5-pro' ou 'gemini-1.0-pro'
            self.llm = ChatGoogleGenerativeAI(model="gemini-1.5-pro", temperature=0)
            logger.info("Modelo Gemini inicializado com sucesso")
        except Exception as e:
            logger.error(f"Erro ao inicializar o modelo Gemini: {str(e)}")
            self.llm = None

        logger.info("Analisador de projetos inicializado")

    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """Extrai texto de um arquivo PDF."""
        try:
            logger.info(f"Extraindo texto do PDF: {pdf_path}")
            text = ""

            # Verificar se é um arquivo de texto (para o exemplo)
            if pdf_path.endswith(".txt"):
                with open(pdf_path, 'r', encoding='utf-8') as file:
                    text = file.read()
            else:
                # Tentar extrair texto do PDF
                try:
                    with open(pdf_path, 'rb') as file:
                        pdf_reader = pypdf.PdfReader(file)
                        for page_num in range(len(pdf_reader.pages)):
                            page = pdf_reader.pages[page_num]
                            text += page.extract_text() + "\n\n"
                except Exception as pdf_error:
                    logger.error(f"Erro ao extrair texto do PDF: {str(pdf_error)}")

                    # Se falhar, tentar abrir como texto (para o exemplo)
                    txt_path = pdf_path.replace(".pdf", ".txt")
                    if os.path.exists(txt_path):
                        with open(txt_path, 'r', encoding='utf-8') as file:
                            text = file.read()
                            logger.info(f"Usando arquivo de texto alternativo: {txt_path}")

            logger.info(f"Texto extraído com sucesso: {len(text)} caracteres")
            return text
        except Exception as e:
            logger.error(f"Erro ao extrair texto do PDF: {str(e)}")
            return ""

    def extract_project_data(self, pdf_text: str) -> Dict[str, Any]:
        """Extrai dados relevantes do texto do projeto usando expressões regulares."""
        try:
            logger.info("Extraindo dados do projeto usando expressões regulares")

            # Inicializar dicionário de dados
            data = {
                "area_terreno": None,
                "area_construida": None,
                "taxa_ocupacao": None,
                "coeficiente_aproveitamento": None,
                "zoneamento": None,
                "tipo_construcao": None,
                "especificacoes_estruturais": {
                    "vigas": None,
                    "pilares": None,
                    "fundacao": None
                }
            }

            # Expressões regulares para extrair informações
            area_terreno_pattern = r'\bÁrea\s+do\s+[Tt]erreno\s*:\s*(\d+(?:[.,]\d+)?)\s*m\u00b2'  # Área do Terreno: 250 m²
            area_construida_pattern = r'\bÁrea\s+[Cc]onstruída\s*:\s*(\d+(?:[.,]\d+)?)\s*m\u00b2'  # Área Construída: 150 m²
            taxa_ocupacao_pattern = r'\b[Tt]axa\s+de\s+[Oo]cupação\s*:\s*(\d+(?:[.,]\d+)?)\s*%'  # Taxa de Ocupação: 60%
            coef_aproveitamento_pattern = r'\b[Cc]oeficiente\s+de\s+[Aa]proveitamento\s*:\s*(\d+(?:[.,]\d+)?)'  # Coeficiente de Aproveitamento: 1.2
            zoneamento_pattern = r'\b[Zz]oneamento\s*:\s*([\w\-]+(?:\s*\([^)]+\))?)'  # Zoneamento: ZM-3 (Zona Mista)
            tipo_construcao_pattern = r'\b[Tt]ipo\s+de\s+[Cc]onstrução\s*:\s*([^\n\r]+)'  # Tipo de Construção: Residencial Unifamiliar

            # Extrair informações estruturais
            vigas_pattern = r'\b[Vv]igas\s*:\s*([^\n\r]+)'  # Vigas: Concreto armado 15x40cm, fck=25MPa
            pilares_pattern = r'\b[Pp]ilares\s*:\s*([^\n\r]+)'  # Pilares: Concreto armado 20x20cm, fck=25MPa
            fundacao_pattern = r'\b[Ff]undação\s*:\s*([^\n\r]+)'  # Fundação: Sapata isolada de concreto armado

            # Aplicar expressões regulares
            area_terreno_match = re.search(area_terreno_pattern, pdf_text)
            if area_terreno_match:
                data["area_terreno"] = float(area_terreno_match.group(1).replace(',', '.'))

            area_construida_match = re.search(area_construida_pattern, pdf_text)
            if area_construida_match:
                data["area_construida"] = float(area_construida_match.group(1).replace(',', '.'))

            taxa_ocupacao_match = re.search(taxa_ocupacao_pattern, pdf_text)
            if taxa_ocupacao_match:
                data["taxa_ocupacao"] = float(taxa_ocupacao_match.group(1).replace(',', '.'))

            coef_aproveitamento_match = re.search(coef_aproveitamento_pattern, pdf_text)
            if coef_aproveitamento_match:
                data["coeficiente_aproveitamento"] = float(coef_aproveitamento_match.group(1).replace(',', '.'))

            zoneamento_match = re.search(zoneamento_pattern, pdf_text)
            if zoneamento_match:
                data["zoneamento"] = zoneamento_match.group(1).strip()

            tipo_construcao_match = re.search(tipo_construcao_pattern, pdf_text)
            if tipo_construcao_match:
                data["tipo_construcao"] = tipo_construcao_match.group(1).strip()

            # Extrair informações estruturais
            vigas_match = re.search(vigas_pattern, pdf_text)
            if vigas_match:
                data["especificacoes_estruturais"]["vigas"] = vigas_match.group(1).strip()

            pilares_match = re.search(pilares_pattern, pdf_text)
            if pilares_match:
                data["especificacoes_estruturais"]["pilares"] = pilares_match.group(1).strip()

            fundacao_match = re.search(fundacao_pattern, pdf_text)
            if fundacao_match:
                data["especificacoes_estruturais"]["fundacao"] = fundacao_match.group(1).strip()

            logger.info("Dados extraídos com sucesso")
            return data

        except Exception as e:
            logger.error(f"Erro ao extrair dados do projeto: {str(e)}")
            return {
                "area_terreno": None,
                "area_construida": None,
                "taxa_ocupacao": None,
                "coeficiente_aproveitamento": None,
                "zoneamento": None,
                "tipo_construcao": None,
                "especificacoes_estruturais": {
                    "vigas": None,
                    "pilares": None,
                    "fundacao": None
                }
            }

    def analyze_compliance(self, project_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analisa a conformidade do projeto com as normas."""
        try:
            logger.info("Analisando conformidade do projeto")

            # Regras de conformidade para São Paulo
            rules = {
                "taxa_ocupacao_maxima": 70.0,  # 70%
                "coeficiente_aproveitamento_maximo": 4.0,
                "zoneamentos_permitidos": [
                    "ZM", "ZEIS", "ZC", "ZPI", "ZDE", "ZMI", "ZCP", "ZCL", "ZOE",
                    "ZEU", "ZEM", "ZEUP", "ZC", "ZEPAM", "ZEPEC", "ZPR", "ZER", "ZLT"
                ]
            }

            # Verificar taxa de ocupação
            taxa_ocupacao_conforme = True
            taxa_ocupacao_mensagem = ""
            if project_data["taxa_ocupacao"] is not None:
                if project_data["taxa_ocupacao"] > rules["taxa_ocupacao_maxima"]:
                    taxa_ocupacao_conforme = False
                    taxa_ocupacao_mensagem = f"Taxa de ocupação ({project_data['taxa_ocupacao']}%) excede o limite permitido ({rules['taxa_ocupacao_maxima']}%)"
                else:
                    taxa_ocupacao_mensagem = f"Taxa de ocupação ({project_data['taxa_ocupacao']}%) está dentro do limite permitido ({rules['taxa_ocupacao_maxima']}%)"

            # Verificar coeficiente de aproveitamento
            coef_aproveitamento_conforme = True
            coef_aproveitamento_mensagem = ""
            if project_data["coeficiente_aproveitamento"] is not None:
                if project_data["coeficiente_aproveitamento"] > rules["coeficiente_aproveitamento_maximo"]:
                    coef_aproveitamento_conforme = False
                    coef_aproveitamento_mensagem = f"Coeficiente de aproveitamento ({project_data['coeficiente_aproveitamento']}) excede o limite permitido ({rules['coeficiente_aproveitamento_maximo']})"
                else:
                    coef_aproveitamento_mensagem = f"Coeficiente de aproveitamento ({project_data['coeficiente_aproveitamento']}) está dentro do limite permitido ({rules['coeficiente_aproveitamento_maximo']})"

            # Verificar zoneamento
            zoneamento_conforme = True
            zoneamento_mensagem = ""
            if project_data["zoneamento"] is not None:
                # Extrair código de zoneamento (ex: ZM-1, ZER-2, etc.)
                zoneamento_code = project_data["zoneamento"].split("-")[0] if "-" in project_data["zoneamento"] else project_data["zoneamento"]

                if zoneamento_code not in rules["zoneamentos_permitidos"]:
                    zoneamento_conforme = False
                    zoneamento_mensagem = f"Zoneamento ({project_data['zoneamento']}) não está entre os permitidos"
                else:
                    zoneamento_mensagem = f"Zoneamento ({project_data['zoneamento']}) está entre os permitidos"

            # Verificar estrutura (simplificado)
            estrutura_conforme = True
            estrutura_mensagem = "Análise estrutural requer avaliação mais detalhada"

            # Resultado final
            em_conformidade = taxa_ocupacao_conforme and coef_aproveitamento_conforme and zoneamento_conforme and estrutura_conforme

            problemas_identificados = []
            if not taxa_ocupacao_conforme:
                problemas_identificados.append("Taxa de ocupação acima do permitido")
            if not coef_aproveitamento_conforme:
                problemas_identificados.append("Coeficiente de aproveitamento acima do permitido")
            if not zoneamento_conforme:
                problemas_identificados.append("Incompatibilidade com zoneamento")
            if not estrutura_conforme:
                problemas_identificados.append("Problemas nos cálculos estruturais")

            resultado = {
                "em_conformidade": em_conformidade,
                "problemas_identificados": problemas_identificados,
                "analises": {
                    "taxa_ocupacao": {
                        "conforme": taxa_ocupacao_conforme,
                        "mensagem": taxa_ocupacao_mensagem
                    },
                    "coeficiente_aproveitamento": {
                        "conforme": coef_aproveitamento_conforme,
                        "mensagem": coef_aproveitamento_mensagem
                    },
                    "zoneamento": {
                        "conforme": zoneamento_conforme,
                        "mensagem": zoneamento_mensagem
                    },
                    "estrutura": {
                        "conforme": estrutura_conforme,
                        "mensagem": estrutura_mensagem
                    }
                }
            }

            logger.info(f"Análise de conformidade concluída: {em_conformidade}")
            return resultado

        except Exception as e:
            logger.error(f"Erro ao analisar conformidade: {str(e)}")
            return {
                "em_conformidade": False,
                "problemas_identificados": ["Erro na análise de conformidade"],
                "analises": {}
            }

    def analyze_project(self, pdf_path: str) -> Dict[str, Any]:
        """Analisa um projeto arquitetônico completo."""
        try:
            logger.info(f"Iniciando análise do projeto: {pdf_path}")

            # Extrair texto do PDF
            pdf_text = self.extract_text_from_pdf(pdf_path)
            if not pdf_text:
                raise ValueError("Não foi possível extrair texto do PDF")

            # Extrair dados do projeto
            project_data = self.extract_project_data(pdf_text)

            # Analisar conformidade
            compliance_result = self.analyze_compliance(project_data)

            # Resultado final
            result = {
                "project_data": project_data,
                "compliance": compliance_result,
                "pdf_path": pdf_path,
                "file_name": os.path.basename(pdf_path)
            }

            logger.info(f"Análise do projeto concluída: {os.path.basename(pdf_path)}")
            return result

        except Exception as e:
            logger.error(f"Erro ao analisar projeto: {str(e)}")
            return {
                "error": str(e),
                "pdf_path": pdf_path,
                "file_name": os.path.basename(pdf_path)
            }

# Função para testar o analisador
def test_analyzer(pdf_path: str):
    """Testa o analisador com um PDF específico."""
    analyzer = ProjectAnalyzer()
    result = analyzer.analyze_project(pdf_path)

    # Salvar resultado em JSON
    output_dir = "analises"
    os.makedirs(output_dir, exist_ok=True)

    output_file = os.path.join(output_dir, f"{os.path.basename(pdf_path)}_analysis.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)

    logger.info(f"Resultado salvo em {output_file}")
    return result

if __name__ == "__main__":
    # Exemplo de uso
    import sys
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
        test_analyzer(pdf_path)
    else:
        print("Uso: python pdf_analyzer.py caminho/para/projeto.pdf")
