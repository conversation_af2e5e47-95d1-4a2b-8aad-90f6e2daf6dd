import os
import re
import logging
import json
from typing import Dict, List, Any, Optional, Tuple
import pypdf
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("pdf_analyzer.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("pdf_analyzer")

# Carregar variáveis de ambiente
load_dotenv()

class ProjectAnalyzer:
    """Classe para analisar projetos arquitetônicos em PDF."""
    
    def __init__(self):
        """Inicializa o analisador de projetos."""
        self.llm = ChatOpenAI(temperature=0, model="gpt-4o")
        logger.info("Analisador de projetos inicializado")
    
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """Extrai texto de um arquivo PDF."""
        try:
            logger.info(f"Extraindo texto do PDF: {pdf_path}")
            text = ""
            with open(pdf_path, 'rb') as file:
                pdf_reader = pypdf.PdfReader(file)
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    text += page.extract_text() + "\n\n"
            
            logger.info(f"Texto extraído com sucesso: {len(text)} caracteres")
            return text
        except Exception as e:
            logger.error(f"Erro ao extrair texto do PDF: {str(e)}")
            return ""
    
    def extract_project_data(self, pdf_text: str) -> Dict[str, Any]:
        """Extrai dados relevantes do texto do projeto usando LLM."""
        try:
            logger.info("Extraindo dados do projeto usando LLM")
            
            system_prompt = """Você é um especialista em análise de projetos arquitetônicos. 
            Extraia as seguintes informações do texto do projeto:
            1. Área do terreno (m²)
            2. Área construída (m²)
            3. Taxa de ocupação (%)
            4. Coeficiente de aproveitamento
            5. Zoneamento da área
            6. Tipo de construção
            7. Especificações estruturais (vigas, pilares, etc.)
            
            Retorne os dados em formato JSON com as seguintes chaves:
            {
                "area_terreno": float,
                "area_construida": float,
                "taxa_ocupacao": float,
                "coeficiente_aproveitamento": float,
                "zoneamento": string,
                "tipo_construcao": string,
                "especificacoes_estruturais": {
                    "vigas": string,
                    "pilares": string,
                    "fundacao": string
                }
            }
            
            Se alguma informação não for encontrada, use null como valor.
            """
            
            # Limitar o texto para não exceder os limites do modelo
            max_text_length = 15000
            if len(pdf_text) > max_text_length:
                pdf_text = pdf_text[:max_text_length] + "..."
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"Aqui está o texto do projeto:\n\n{pdf_text}")
            ]
            
            response = self.llm.invoke(messages)
            
            # Extrair o JSON da resposta
            json_match = re.search(r'```json\s*(.*?)\s*```', response.content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                json_str = response.content
            
            # Limpar o JSON
            json_str = re.sub(r'[^\x00-\x7F]+', '', json_str)
            
            # Converter para dicionário
            data = json.loads(json_str)
            logger.info("Dados extraídos com sucesso")
            return data
        
        except Exception as e:
            logger.error(f"Erro ao extrair dados do projeto: {str(e)}")
            return {
                "area_terreno": None,
                "area_construida": None,
                "taxa_ocupacao": None,
                "coeficiente_aproveitamento": None,
                "zoneamento": None,
                "tipo_construcao": None,
                "especificacoes_estruturais": {
                    "vigas": None,
                    "pilares": None,
                    "fundacao": None
                }
            }
    
    def analyze_compliance(self, project_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analisa a conformidade do projeto com as normas."""
        try:
            logger.info("Analisando conformidade do projeto")
            
            # Regras de conformidade para São Paulo
            rules = {
                "taxa_ocupacao_maxima": 70.0,  # 70%
                "coeficiente_aproveitamento_maximo": 4.0,
                "zoneamentos_permitidos": [
                    "ZM", "ZEIS", "ZC", "ZPI", "ZDE", "ZMI", "ZCP", "ZCL", "ZOE",
                    "ZEU", "ZEM", "ZEUP", "ZC", "ZEPAM", "ZEPEC", "ZPR", "ZER", "ZLT"
                ]
            }
            
            # Verificar taxa de ocupação
            taxa_ocupacao_conforme = True
            taxa_ocupacao_mensagem = ""
            if project_data["taxa_ocupacao"] is not None:
                if project_data["taxa_ocupacao"] > rules["taxa_ocupacao_maxima"]:
                    taxa_ocupacao_conforme = False
                    taxa_ocupacao_mensagem = f"Taxa de ocupação ({project_data['taxa_ocupacao']}%) excede o limite permitido ({rules['taxa_ocupacao_maxima']}%)"
                else:
                    taxa_ocupacao_mensagem = f"Taxa de ocupação ({project_data['taxa_ocupacao']}%) está dentro do limite permitido ({rules['taxa_ocupacao_maxima']}%)"
            
            # Verificar coeficiente de aproveitamento
            coef_aproveitamento_conforme = True
            coef_aproveitamento_mensagem = ""
            if project_data["coeficiente_aproveitamento"] is not None:
                if project_data["coeficiente_aproveitamento"] > rules["coeficiente_aproveitamento_maximo"]:
                    coef_aproveitamento_conforme = False
                    coef_aproveitamento_mensagem = f"Coeficiente de aproveitamento ({project_data['coeficiente_aproveitamento']}) excede o limite permitido ({rules['coeficiente_aproveitamento_maximo']})"
                else:
                    coef_aproveitamento_mensagem = f"Coeficiente de aproveitamento ({project_data['coeficiente_aproveitamento']}) está dentro do limite permitido ({rules['coeficiente_aproveitamento_maximo']})"
            
            # Verificar zoneamento
            zoneamento_conforme = True
            zoneamento_mensagem = ""
            if project_data["zoneamento"] is not None:
                # Extrair código de zoneamento (ex: ZM-1, ZER-2, etc.)
                zoneamento_code = project_data["zoneamento"].split("-")[0] if "-" in project_data["zoneamento"] else project_data["zoneamento"]
                
                if zoneamento_code not in rules["zoneamentos_permitidos"]:
                    zoneamento_conforme = False
                    zoneamento_mensagem = f"Zoneamento ({project_data['zoneamento']}) não está entre os permitidos"
                else:
                    zoneamento_mensagem = f"Zoneamento ({project_data['zoneamento']}) está entre os permitidos"
            
            # Verificar estrutura (simplificado)
            estrutura_conforme = True
            estrutura_mensagem = "Análise estrutural requer avaliação mais detalhada"
            
            # Resultado final
            em_conformidade = taxa_ocupacao_conforme and coef_aproveitamento_conforme and zoneamento_conforme and estrutura_conforme
            
            problemas_identificados = []
            if not taxa_ocupacao_conforme:
                problemas_identificados.append("Taxa de ocupação acima do permitido")
            if not coef_aproveitamento_conforme:
                problemas_identificados.append("Coeficiente de aproveitamento acima do permitido")
            if not zoneamento_conforme:
                problemas_identificados.append("Incompatibilidade com zoneamento")
            if not estrutura_conforme:
                problemas_identificados.append("Problemas nos cálculos estruturais")
            
            resultado = {
                "em_conformidade": em_conformidade,
                "problemas_identificados": problemas_identificados,
                "analises": {
                    "taxa_ocupacao": {
                        "conforme": taxa_ocupacao_conforme,
                        "mensagem": taxa_ocupacao_mensagem
                    },
                    "coeficiente_aproveitamento": {
                        "conforme": coef_aproveitamento_conforme,
                        "mensagem": coef_aproveitamento_mensagem
                    },
                    "zoneamento": {
                        "conforme": zoneamento_conforme,
                        "mensagem": zoneamento_mensagem
                    },
                    "estrutura": {
                        "conforme": estrutura_conforme,
                        "mensagem": estrutura_mensagem
                    }
                }
            }
            
            logger.info(f"Análise de conformidade concluída: {em_conformidade}")
            return resultado
        
        except Exception as e:
            logger.error(f"Erro ao analisar conformidade: {str(e)}")
            return {
                "em_conformidade": False,
                "problemas_identificados": ["Erro na análise de conformidade"],
                "analises": {}
            }
    
    def analyze_project(self, pdf_path: str) -> Dict[str, Any]:
        """Analisa um projeto arquitetônico completo."""
        try:
            logger.info(f"Iniciando análise do projeto: {pdf_path}")
            
            # Extrair texto do PDF
            pdf_text = self.extract_text_from_pdf(pdf_path)
            if not pdf_text:
                raise ValueError("Não foi possível extrair texto do PDF")
            
            # Extrair dados do projeto
            project_data = self.extract_project_data(pdf_text)
            
            # Analisar conformidade
            compliance_result = self.analyze_compliance(project_data)
            
            # Resultado final
            result = {
                "project_data": project_data,
                "compliance": compliance_result,
                "pdf_path": pdf_path,
                "file_name": os.path.basename(pdf_path)
            }
            
            logger.info(f"Análise do projeto concluída: {os.path.basename(pdf_path)}")
            return result
        
        except Exception as e:
            logger.error(f"Erro ao analisar projeto: {str(e)}")
            return {
                "error": str(e),
                "pdf_path": pdf_path,
                "file_name": os.path.basename(pdf_path)
            }

# Função para testar o analisador
def test_analyzer(pdf_path: str):
    """Testa o analisador com um PDF específico."""
    analyzer = ProjectAnalyzer()
    result = analyzer.analyze_project(pdf_path)
    
    # Salvar resultado em JSON
    output_dir = "analises"
    os.makedirs(output_dir, exist_ok=True)
    
    output_file = os.path.join(output_dir, f"{os.path.basename(pdf_path)}_analysis.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    logger.info(f"Resultado salvo em {output_file}")
    return result

if __name__ == "__main__":
    # Exemplo de uso
    import sys
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
        test_analyzer(pdf_path)
    else:
        print("Uso: python pdf_analyzer.py caminho/para/projeto.pdf")
