from fastapi import FastAPI, File, UploadFile, BackgroundTasks, HTTPException
from fastapi.responses import JSONResponse
import uvicorn
import os
import uuid
from pydantic import BaseModel
from typing import List, Optional
import shutil
import asyncio
from datetime import datetime

app = FastAPI(title="API de Análise de Projetos Arquitetônicos")

# Modelos de dados
class AnalysisResult(BaseModel):
    project_id: str
    status: str
    timestamp: str
    taxa_ocupacao: Optional[dict] = None
    zoneamento: Optional[dict] = None
    estrutura: Optional[dict] = None
    score_geral: Optional[float] = None

class AnalysisRequest(BaseModel):
    project_name: str
    region_code: str
    analysis_types: List[str]

# Diretórios para armazenamento
UPLOAD_DIR = "uploads"
RESULTS_DIR = "results"
os.makedirs(UPLOAD_DIR, exist_ok=True)
os.makedirs(RESULTS_DIR, exist_ok=True)

# Simulação de banco de dados (substituir por DB real)
analysis_database = {}

# Função para processar arquivo em background
async def process_project(file_path: str, project_id: str, analysis_types: List[str]):
    try:
        # Simular processamento (substituir por análise real)
        await asyncio.sleep(10)  # Simular 10 segundos de processamento
        
        # Resultado fictício (substituir por análise real)
        result = AnalysisResult(
            project_id=project_id,
            status="completed",
            timestamp=datetime.now().isoformat(),
            taxa_ocupacao={
                "valor": 0.65,
                "permitido": 0.7,
                "conformidade": True,
                "detalhes": "Projeto atende aos requisitos de taxa de ocupação"
            },
            zoneamento={
                "zona": "ZM-3",
                "uso_permitido": True,
                "detalhes": "Uso residencial multifamiliar permitido na zona ZM-3"
            },
            estrutura={
                "conformidade": True,
                "issues": [],
                "detalhes": "Cálculos estruturais validados sem problemas"
            },
            score_geral=0.92
        )
        
        # Salvar resultado no "banco de dados"
        analysis_database[project_id] = result.dict()
        
        # Salvar resultado em arquivo
        with open(f"{RESULTS_DIR}/{project_id}.json", "w") as f:
            import json
            json.dump(result.dict(), f, indent=2)
            
    except Exception as e:
        # Em caso de erro, atualizar status
        analysis_database[project_id] = {
            "project_id": project_id,
            "status": "failed",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

# Endpoint para upload de projeto
@app.post("/projects/analyze")
async def analyze_project(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    project_name: str = None,
    region_code: str = "SP",
    analysis_types: str = "taxa_ocupacao,zoneamento,estrutura"
):
    # Validar tipo de arquivo
    if not file.filename.endswith(('.pdf', '.dwg')):
        raise HTTPException(status_code=400, detail="Apenas arquivos PDF e DWG são permitidos")
    
    # Gerar ID único para o projeto
    project_id = str(uuid.uuid4())
    
    # Salvar arquivo
    file_path = f"{UPLOAD_DIR}/{project_id}_{file.filename}"
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
    
    # Inicializar status no "banco de dados"
    analysis_database[project_id] = {
        "project_id": project_id,
        "status": "processing",
        "timestamp": datetime.now().isoformat(),
        "file_name": file.filename,
        "project_name": project_name or file.filename,
        "region_code": region_code
    }
    
    # Iniciar processamento em background
    background_tasks.add_task(
        process_project, 
        file_path, 
        project_id, 
        analysis_types.split(",")
    )
    
    return JSONResponse({
        "project_id": project_id,
        "status": "processing",
        "message": "Análise iniciada. Use o ID do projeto para verificar o status."
    })

# Endpoint para verificar status da análise
@app.get("/projects/{project_id}")
async def get_project_status(project_id: str):
    if project_id not in analysis_database:
        raise HTTPException(status_code=404, detail="Projeto não encontrado")
    
    return JSONResponse(analysis_database[project_id])

# Endpoint para plugin AutoCAD
@app.post("/integrations/autocad")
async def autocad_integration(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    project_metadata: str = None
):
    # Implementação similar ao endpoint principal
    # com adaptações específicas para o AutoCAD
    project_id = str(uuid.uuid4())
    
    return JSONResponse({
        "project_id": project_id,
        "status": "processing",
        "integration": "autocad"
    })

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)