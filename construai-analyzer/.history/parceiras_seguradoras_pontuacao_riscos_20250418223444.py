from typing import Dict, List, Optional, Union, Any
from pydantic import BaseModel, Field
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
import joblib
import os
from datetime import datetime

# Modelos de dados para avaliação de risco
class BuildingAttribute(BaseModel):
    name: str
    value: Union[float, str, bool]
    weight: float = 1.0
    category: str

class RiskFactor(BaseModel):
    name: str
    description: str
    weight: float
    threshold: Optional[float] = None
    attributes: List[str]

class RiskScoreResult(BaseModel):
    total_score: float
    risk_level: str  # "Low", "Medium", "High", "Very High"
    factor_scores: Dict[str, float]
    recommendations: List[str]
    timestamp: str

# Sistema de pontuação de risco
class RiskScoringSystem:
    def __init__(self, model_path: Optional[str] = None):
        self.risk_factors = self._initialize_risk_factors()
        
        # Carregar modelo de ML se disponível
        self.ml_model = None
        if model_path and os.path.exists(model_path):
            self.ml_model = joblib.load(model_path)
    
    def _initialize_risk_factors(self) -> Dict[str, RiskFactor]:
        """Inicializa os fatores de risco usados na avaliação."""
        return {
            "structural_integrity": RiskFactor(
                name="structural_integrity",
                description="Integridade estrutural do edifício",
                weight=0.35,
                attributes=["beam_load_ratio", "column_strength", "foundation_type"]
            ),
            "code_compliance": RiskFactor(
                name="code_compliance",
                description="Conformidade com códigos de construção",
                weight=0.25,
                attributes=["zoning_violations", "occupancy_violation", "safety_features"]
            ),
            "material_quality": RiskFactor(
                name="material_quality",
                description="Qualidade dos materiais utilizados",
                weight=0.20,
                attributes=["concrete_strength", "steel_quality", "material_certification"]
            ),
            "design_quality": RiskFactor(
                name="design_quality",
                description="Qualidade do projeto arquitetônico",
                weight=0.15,
                attributes=["design_complexity", "architect_experience", "design_modifications"]
            ),
            "environmental_factors": RiskFactor(
                name="environmental_factors",
                description="Fatores ambientais e de localização",
                weight=0.05,
                attributes=["flood_zone", "seismic_zone", "soil_quality"]
            )
        }
    
    def _map_risk_level(self, score: float) -> str:
        """Mapeia a pontuação numérica para um nível de risco."""
        if score >= 0.9:
            return "Low"
        elif score >= 0.75:
            return "Medium"
        elif score >= 0.6:
            return "High"
        else:
            return "Very High"
    
    def _generate_recommendations(self, attribute_scores: Dict[str, float]) -> List[str]:
        """Gera recomendações baseadas nas pontuações de atributos baixas."""
        recommendations = []
        
        # Identificar atributos com pontuação baixa
        low_scores = {k: v for k, v in attribute_scores.items() if v < 0.7}
        
        # Recomendações específicas para cada atributo
        recommendation_map = {
            "beam_load_ratio": "Revise os cálculos de carga nas vigas para garantir margens de segurança adequadas.",
            "column_strength": "Considere reforçar as colunas estruturais para melhorar a integridade do edifício.",
            "zoning_violations": "Resolva as violações de zoneamento identificadas antes de prosseguir com a construção.",
            "occupancy_violation": "Ajuste a taxa de ocupação para atender aos requisitos regulatórios locais.",
            "concrete_strength": "Utilize concreto com maior resistência para melhorar a durabilidade da estrutura.",
            "design_complexity": "Simplifique elementos de design complexos que possam comprometer a integridade estrutural.",
            "flood_zone": "Implemente medidas adicionais de proteção contra inundações devido à localização em zona de risco."
        }
        
        for attr in low_scores:
            if attr in recommendation_map:
                recommendations.append(recommendation_map[attr])
        
        # Recomendações gerais baseadas nos fatores de risco
        if len(low_scores) > 0:
            recommendations.append("Considere contratar uma revisão independente do projeto para identificar pontos de melhoria.")
        
        return recommendations
    
    def calculate_score(self, project_attributes: Dict[str, Any]) -> RiskScoreResult:
        """Calcula pontuação de risco para um projeto com base em seus atributos."""
        # Converter atributos para formato padrão
        attributes = {}