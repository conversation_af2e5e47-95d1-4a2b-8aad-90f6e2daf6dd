from fastapi import FastAP<PERSON>, Depends, HTTPException, Body
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Boolean, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session, relationship
from pydantic import BaseModel
from datetime import datetime, timedelta
import stripe
import os
from typing import List, Optional
import secrets

# Configuração do banco de dados
DATABASE_URL = "postgresql://user:password@localhost/archanalysis"
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Configuração do Stripe
stripe.api_key = os.getenv("STRIPE_API_KEY", "sk_test_...")

# Modelos de banco de dados
class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True)
    company_name = Column(String)
    hashed_password = Column(String)
    api_key = Column(String, unique=True, index=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    subscription = relationship("Subscription", back_populates="user", uselist=False)
    projects = relationship("Project", back_populates="user")

class Subscription(Base):
    __tablename__ = "subscriptions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    stripe_customer_id = Column(String)
    stripe_subscription_id = Column(String)
    plan_type = Column(String)  # "per_project" ou "unlimited"
    status = Column(String)
    current_period_end = Column(DateTime)
    
    user = relationship("User", back_populates="subscription")

class Project(Base):
    __tablename__ = "projects"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    external_id = Column(String, unique=True)
    name = Column(String)
    status = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)
    billed = Column(Boolean, default=False)
    
    user = relationship("User", back_populates="projects")

# Criar tabelas
Base.metadata.create_all(bind=engine)

# Modelos Pydantic
class UserCreate(BaseModel):
    email: str
    password: str
    company_name: str

class UserResponse(BaseModel):
    id: int
    email: str
    company_name: str
    api_key: str
    is_active: bool
    
    class Config:
        orm_mode = True

class SubscriptionCreate(BaseModel):
    plan_type: str
    payment_method_id: str

class SubscriptionResponse(BaseModel):
    id: int
    plan_type: str
    status: str
    current_period_end: datetime
    
    class Config:
        orm_mode = True

# Funções auxiliares
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def hash_password(password: str) -> str:
    # Implementar hash seguro (como bcrypt)
    return f"hashed_{password}"

def verify_password(plain_password: str, hashed_password: str) -> bool:
    # Verificar senha com biblioteca segura
    return hashed_password == f"hashed_{plain_password}"

def create_api_key() -> str:
    return secrets.token_urlsafe(32)

# Aplicação FastAPI
app = FastAPI(title="Sistema de Pagamento - ArchAnalysis")

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# Middleware de autenticação
async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    user = db.query(User).filter(User.api_key == token).first()
    if not user:
        raise HTTPException(status_code=401, detail="Credenciais inválidas")
    return user

# Endpoints
@app.post("/users/", response_model=UserResponse)
async def create_user(user: UserCreate, db: Session = Depends(get_db)):
    db_user = db.query(User).filter(User.email == user.email).first()
    if db_user:
        raise HTTPException(status_code=400, detail="Email já cadastrado")
    
    hashed_password = hash_password(user.password)
    api_key = create_api_key()
    
    db_user = User(
        email=user.email,
        hashed_password=hashed_password,
        company_name=user.company_name,
        api_key=api_key
    )
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    return db_user

@app.post("/subscriptions/", response_model=SubscriptionResponse)
async def create_subscription(
    sub_data: SubscriptionCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verificar se já tem assinatura
    if current_user.subscription and current_user.subscription.status == "active":
        raise HTTPException(status_code=400, detail="Usuário já possui assinatura ativa")
    
    # Criar ou recuperar cliente no Stripe
    if current_user.subscription and current_user.subscription.stripe_customer_id:
        stripe_customer_id = current_user.subscription.stripe_customer_id
    else:
        customer = stripe.Customer.create(
            email=current_user.email,
            name=current_user.company_name,
            payment_method=sub_data.payment_method_id,
            invoice_settings={"default_payment_method": sub_data.payment_method_id}
        )
        stripe_customer_id = customer.id
    
    # Definir preço com base no plano
    price_id = ""
    if sub_data.plan_type == "per_project":
        price_id = "price_per_project"  # Substituir pelo ID real no Stripe
    elif sub_data.plan_type == "unlimited":
        price_id = "price_unlimited"  # Substituir pelo ID real no Stripe
    else:
        raise HTTPException(status_code=400, detail="Tipo de plano inválido")
    
    # Criar assinatura no Stripe
    subscription = stripe.Subscription.create(
        customer=stripe_customer_id,
        items=[{"price": price_id}],
        expand=["latest_invoice.payment_intent"]
    )
    
    # Salvar dados da assinatura
    db_subscription = Subscription(
        user_id=current_user.id,
        stripe_customer_id=stripe_customer_id,
        stripe_subscription_id=subscription.id,
        plan_type=sub_data.plan_type,
        status=subscription.status,
        current_period_end=datetime.fromtimestamp(subscription.current_period_end)
    )
    
    db.add(db_subscription)
    db.commit()
    db.refresh(db_subscription)
    
    return db_subscription

@app.post("/projects/charge")
async def charge_for_project(
    project_id: str = Body(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verificar se o projeto existe
    project = db.query(Project).filter(Project.external_id == project_id).first()
    if not project:
        raise HTTPException(status_code=404, detail="Projeto não encontrado")
    
    # Verificar se já foi cobrado
    if project.billed:
        raise HTTPException(status_code=400, detail="Projeto já foi cobrado")
    
    # Verificar assinatura
    subscription = current_user.subscription
    if not subscription:
        raise HTTPException(status_code=400, detail="Usuário não possui assinatura")
    
    # Se assinatura for por projeto, cobrar
    if subscription.plan_type == "per_project":
        # Criar cobrança no Stripe
        charge = stripe.PaymentIntent.create(
            amount=29900,  # R$299,00 em centavos
            currency="brl",
            customer=subscription.stripe_customer_id,
            description=f"Análise do projeto: {project.name}"
        )
        
        # Marcar projeto como cobrado
        project.billed = True
        db.commit()
        
        return {"message": "Cobrança realizada com sucesso", "charge_id": charge.id}
    else:
        # Plano ilimitado, apenas registrar uso
        return {"message": "Projeto incluído no plano ilimitado"}

@app.get("/webhook/stripe")
async def stripe_webhook(db: Session = Depends(get_db)):
    # Implementar webhook para processar eventos do Stripe
    # como falhas de pagamento, cancelamentos, etc.
    pass