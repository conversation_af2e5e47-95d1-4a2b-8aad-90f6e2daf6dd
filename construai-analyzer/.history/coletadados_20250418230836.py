import requests
from bs4 import BeautifulSoup
import pandas as pd
import os
import time
import logging
from concurrent.futures import ThreadPoolExecutor
from dotenv import load_dotenv

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("coletadados.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("coletadados")

# Carregar variáveis de ambiente
load_dotenv()

# Configurações
base_url = "https://geosampa.prefeitura.sp.gov.br/"
output_dir = "projetos_data"
max_workers = 5
max_retries = 3
retry_delay = 2  # segundos

# Criar diretórios necessários
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    logger.info(f"Diretório {output_dir} criado com sucesso")
else:
    logger.info(f"Diretório {output_dir} já existe")

# Criar subdiretórios para organização
pdf_dir = os.path.join(output_dir, "pdfs")
if not os.path.exists(pdf_dir):
    os.makedirs(pdf_dir)
    logger.info(f"Diretório {pdf_dir} criado com sucesso")

# Função para baixar um PDF com retry
def download_pdf(url, filename):
    for _ in range(max_retries):
        try:
            response = requests.get(url, stream=True, timeout=30)
            if response.status_code == 200:
                file_path = os.path.join(pdf_dir, filename)
                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                logger.info(f"Downloaded: {filename}")
                return True
            else:
                logger.warning(f"Failed to download {url}: Status code {response.status_code}")
                time.sleep(retry_delay)
        except Exception as e:
            logger.error(f"Error downloading {url}: {str(e)}")
            time.sleep(retry_delay)

    logger.error(f"Failed to download {url} after {max_retries} attempts")
    return False

# Função para coletar links de projetos arquitetônicos do GeoSampa
def collect_pdf_links(category_index=0):
    for _ in range(max_retries):
        try:
            # URLs e categorias do GeoSampa
            categories = [
                {
                    "name": "Legislação Urbana",
                    "url": "https://geosampa.prefeitura.sp.gov.br/PaginasPublicas/SBCamadas.aspx",
                    "category_id": "13"
                },
                {
                    "name": "Cadastro",
                    "url": "https://geosampa.prefeitura.sp.gov.br/PaginasPublicas/SBCamadas.aspx",
                    "category_id": "11"
                }
            ]

            if category_index >= len(categories):
                logger.warning(f"Categoria de índice {category_index} não existe. Usando categoria 0.")
                category_index = 0

            category = categories[category_index]
            logger.info(f"Acessando categoria: {category['name']}")

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3',
                'Referer': 'https://geosampa.prefeitura.sp.gov.br/',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Cache-Control': 'max-age=0'
            }

            # Primeiro, acessamos a página principal para obter cookies e sessão
            session = requests.Session()
            response = session.get(category['url'], headers=headers, timeout=30)

            if response.status_code != 200:
                logger.warning(f"Status code inesperado: {response.status_code}")
                time.sleep(retry_delay)
                continue

            # Agora vamos simular a navegação para a categoria específica
            # Exemplo de URL de download: https://download.geosampa.prefeitura.sp.gov.br/PaginasPublicas/downloadArquivo.aspx?orig=DownloadCamadas&arq=13_Legisla%E7%E3o%20Urbana%5C%5CZoneamento_Lei16402-16_Mapa_01_Principal%5C%5CShapefile%5C%5CMapa-1-SHP&arqTipo=Shapefile

            # Vamos buscar os links de download diretamente
            download_base_url = "https://download.geosampa.prefeitura.sp.gov.br/PaginasPublicas/downloadArquivo.aspx"

            # Padrões de arquivos que queremos baixar (projetos arquitetônicos, zoneamento, etc)
            patterns = [
                "Zoneamento",
                "Plano_Diretor",
                "Edificacoes",
                "Lotes",
                "Quadras",
                "Projetos"
            ]

            # Formatos de arquivo que queremos
            formats = ["PDF", "Shapefile", "DWG"]

            # Vamos criar links manualmente com base nos padrões conhecidos
            links = []

            for pattern in patterns:
                for fmt in formats:
                    # Criar URL de download
                    file_path = f"{category['category_id']}_{category['name']}\\\\{pattern}"
                    download_url = f"{download_base_url}?orig=DownloadCamadas&arq={file_path}&arqTipo={fmt}"

                    # Nome do arquivo
                    filename = f"{pattern}_{category['name']}_{fmt}.pdf"

                    # Adicionar metadados
                    metadata = {
                        'title': f"{pattern} - {category['name']}",
                        'source_category': category['name'],
                        'format': fmt,
                        'url': download_url
                    }

                    links.append((download_url, filename, metadata))

            # Também vamos tentar extrair links da página
            soup = BeautifulSoup(response.content, 'html.parser')

            # Procurar por links de download na página
            for a in soup.find_all('a'):
                if a.has_attr('href') and ('download' in a['href'].lower() or '.pdf' in a['href'].lower()):
                    href = a['href']

                    # Verificar se é um link relativo ou absoluto
                    if href.startswith('http'):
                        full_url = href
                    elif href.startswith('/'):
                        full_url = f"https://geosampa.prefeitura.sp.gov.br{href}"
                    else:
                        full_url = f"https://geosampa.prefeitura.sp.gov.br/{href}"

                    # Extrair nome do arquivo
                    filename = href.split('/')[-1]
                    if not filename or '?' in filename:
                        filename = f"documento_{len(links) + 1}.pdf"

                    # Adicionar metadados
                    metadata = {
                        'title': a.get_text().strip() if a.get_text() else filename,
                        'source_category': category['name'],
                        'extracted': True,
                        'url': full_url
                    }

                    # Verificar se já temos este link
                    if not any(link[0] == full_url for link in links):
                        links.append((full_url, filename, metadata))

            logger.info(f"Encontrados {len(links)} links potenciais na categoria {category['name']}")
            return links

        except Exception as e:
            logger.error(f"Erro ao coletar links da página {page_number}: {str(e)}")
            time.sleep(retry_delay)

    logger.error(f"Falha ao coletar links da página {page_number} após {max_retries} tentativas")
    return []

def main():
    # Coletar links de múltiplas páginas
    all_links = []
    target_count = 5000  # Meta de 5.000 documentos
    max_pages = 200      # Limite de páginas para evitar loops infinitos

    logger.info("Iniciando coleta de links de projetos...")

    for page in range(1, max_pages + 1):
        logger.info(f"Coletando links da página {page}...")
        page_links = collect_pdf_links(page)
        all_links.extend(page_links)

        # Verificar se já atingimos a meta
        if len(all_links) >= target_count:
            logger.info(f"Meta de {target_count} links atingida!")
            break

        # Pausa para não sobrecarregar o servidor
        time.sleep(2)

    logger.info(f"Total de links coletados: {len(all_links)}")

    # Salvar metadados dos links em CSV antes do download
    metadata_df = pd.DataFrame([
        {
            'url': link[0],
            'filename': link[1],
            **link[2]  # Desempacotar os metadados adicionais
        } for link in all_links
    ])

    metadata_path = os.path.join(output_dir, "metadata.csv")
    metadata_df.to_csv(metadata_path, index=False)
    logger.info(f"Metadados salvos em {metadata_path}")

    # Baixar PDFs usando multithreading
    logger.info("Iniciando download dos PDFs...")
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Passar apenas URL e filename para a função de download
        download_tasks = [(link[0], link[1]) for link in all_links]
        results = list(executor.map(lambda x: download_pdf(x[0], x[1]), download_tasks))

    success_count = sum(results)
    logger.info(f"Total de PDFs baixados com sucesso: {success_count}/{len(all_links)}")

    # Atualizar metadados com status de download
    metadata_df['download_success'] = results
    metadata_df.to_csv(metadata_path, index=False)
    logger.info("Metadados atualizados com status de download")

    return success_count

# Executar o script se for o arquivo principal
if __name__ == "__main__":
    main()