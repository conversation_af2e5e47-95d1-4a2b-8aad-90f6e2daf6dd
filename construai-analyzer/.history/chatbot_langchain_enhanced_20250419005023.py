import streamlit as st
import os
import json
import time
import tempfile
import logging
from datetime import datetime
from dotenv import load_dotenv
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

# Importações do LangChain
from langchain.memory import ConversationBufferMemory
from langchain.chains import Conversational<PERSON><PERSON><PERSON><PERSON><PERSON>hain
from langchain_community.document_loaders import PyPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import FAISS

# Importações do Google Gemini
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_google_genai import GoogleGenerativeAIEmbeddings
import google.generativeai as genai

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("chatbot.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("chatbot")

# Carregar variáveis de ambiente
load_dotenv()

# Obter configurações do arquivo .env
chunk_size = int(os.getenv('CHUNK_SIZE', 1000))
chunk_overlap = int(os.getenv('CHUNK_OVERLAP', 200))

# Configurar Google Gemini
api_key = os.getenv("GOOGLE_API_KEY")
if api_key:
    genai.configure(api_key=api_key)
    logger.info("Google Gemini API configurada com sucesso")
else:
    logger.warning("Chave da API do Google Gemini não encontrada no arquivo .env")

# Configurar página
st.set_page_config(
    page_title="Analisador de Projetos Arquitetônicos",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Adicionar informações de preço na barra lateral
st.sidebar.title("Planos de Assinatura")
st.sidebar.info("""
### Planos disponíveis:
- **Básico**: R$ 299/projeto analisado
- **Premium**: R$ 1.999/mês para consultorias ilimitadas

Entre em contato para mais informações sobre planos corporativos.
""")

st.sidebar.title("Sobre")
st.sidebar.info("""
### ConstruAI Analyzer
Análise automatizada de projetos arquitetônicos usando IA.

Detectamos:
- Violações de taxa de ocupação
- Incompatibilidades com zoneamento
- Erros em cálculos estruturais
""")

# Título principal
st.title("Analisador Automático de Projetos Arquitetônicos")
st.markdown("Envie seu projeto em PDF e receba a análise em minutos.")

# Inicializar variáveis de estado da sessão
if 'chat_history' not in st.session_state:
    st.session_state.chat_history = []
if 'document_processed' not in st.session_state:
    st.session_state.document_processed = False
if 'analysis_results' not in st.session_state:
    st.session_state.analysis_results = {}
if 'user_info' not in st.session_state:
    st.session_state.user_info = {"logged_in": False}

# Importar o analisador de PDF
from pdf_analyzer import ProjectAnalyzer

# Função para processar PDF
def process_pdf(pdf_file):
    try:
        with st.spinner("Processando seu projeto... Isso pode levar até 3 minutos."):
            logger.info(f"Iniciando processamento de arquivo: {pdf_file.name}")

            # Salvar PDF temporariamente
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp_file:
                tmp_file.write(pdf_file.read())
                pdf_path = tmp_file.name
                logger.info(f"PDF salvo temporariamente em: {pdf_path}")

            # Usar o analisador de projetos para análise avançada
            analyzer = ProjectAnalyzer()
            project_analysis = analyzer.analyze_project(pdf_path)

            logger.info("Análise de projeto concluída")

            # Carregar e processar PDF para o chatbot
            loader = PyPDFLoader(pdf_path)
            documents = loader.load()
            logger.info(f"PDF carregado com {len(documents)} páginas")

            # Dividir texto em chunks
            text_splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=chunk_overlap)
            chunks = text_splitter.split_documents(documents)
            logger.info(f"Documento dividido em {len(chunks)} chunks")

            # Criar embeddings e banco de vetores usando Google Gemini
            # O modelo correto para embeddings é 'models/embedding-001'
            embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
            vectorstore = FAISS.from_documents(chunks, embeddings)
            logger.info("Embeddings e banco de vetores criados com Google Gemini")

            # Configurar modelo de chat usando Google Gemini
            # O modelo correto para chat é 'gemini-1.5-pro' ou 'gemini-1.0-pro'
            llm = ChatGoogleGenerativeAI(model="gemini-1.5-pro", temperature=0)
            memory = ConversationBufferMemory(memory_key="chat_history", return_messages=True)

            # Criar chain de conversação
            conversation_chain = ConversationalRetrievalChain.from_llm(
                llm=llm,
                retriever=vectorstore.as_retriever(),
                memory=memory
            )
            logger.info("Chain de conversação criada")

            # Preparar análises para exibição
            analysis = {}

            # Verificar se temos dados do projeto
            if "project_data" in project_analysis and project_analysis["project_data"]:
                project_data = project_analysis["project_data"]

                # Análise de taxa de ocupação
                if "compliance" in project_analysis and "analises" in project_analysis["compliance"]:
                    compliance = project_analysis["compliance"]

                    # Taxa de ocupação
                    if "taxa_ocupacao" in compliance["analises"]:
                        taxa_info = compliance["analises"]["taxa_ocupacao"]
                        analysis["taxa_ocupacao"] = f"Taxa de ocupação: {project_data.get('taxa_ocupacao', 'N/A')}%\n{taxa_info.get('mensagem', '')}"
                    else:
                        analysis["taxa_ocupacao"] = "Não foi possível analisar a taxa de ocupação."

                    # Zoneamento
                    if "zoneamento" in compliance["analises"]:
                        zona_info = compliance["analises"]["zoneamento"]
                        analysis["zoneamento"] = f"Zoneamento: {project_data.get('zoneamento', 'N/A')}\nTipo de construção: {project_data.get('tipo_construcao', 'N/A')}\n{zona_info.get('mensagem', '')}"
                    else:
                        analysis["zoneamento"] = "Não foi possível analisar o zoneamento."

                    # Cálculos estruturais
                    if "estrutura" in compliance["analises"]:
                        estrutura_info = compliance["analises"]["estrutura"]

                        # Obter especificações estruturais
                        especificacoes = project_data.get('especificacoes_estruturais', {})
                        vigas = especificacoes.get('vigas', 'N/A')
                        pilares = especificacoes.get('pilares', 'N/A')
                        fundacao = especificacoes.get('fundacao', 'N/A')

                        analysis["calculos_estruturais"] = f"Especificações estruturais:\n- Vigas: {vigas}\n- Pilares: {pilares}\n- Fundação: {fundacao}\n\n{estrutura_info.get('mensagem', '')}"
                    else:
                        analysis["calculos_estruturais"] = "Não foi possível analisar os cálculos estruturais."

                    # Resumo de conformidade
                    analysis["resumo"] = {
                        "em_conformidade": compliance.get("em_conformidade", False),
                        "problemas_identificados": compliance.get("problemas_identificados", [])
                    }
                else:
                    # Caso não tenha análise de conformidade
                    analysis = {
                        "taxa_ocupacao": f"Taxa de ocupação: {project_data.get('taxa_ocupacao', 'N/A')}%",
                        "zoneamento": f"Zoneamento: {project_data.get('zoneamento', 'N/A')}\nTipo de construção: {project_data.get('tipo_construcao', 'N/A')}",
                        "calculos_estruturais": "Não foi possível analisar os cálculos estruturais.",
                        "resumo": {
                            "em_conformidade": False,
                            "problemas_identificados": ["Não foi possível determinar a conformidade"]
                        }
                    }
            else:
                # Fallback para análise manual via LLM se o analisador não conseguir extrair dados
                logger.warning("Analisador não conseguiu extrair dados. Usando análise via LLM.")

                # Análise de taxa de ocupação
                taxa_ocupacao_prompt = """Analise cuidadosamente este projeto e extraia:
                1. A taxa de ocupação projetada (percentual da área do terreno ocupada pela construção).
                2. Compare com o limite máximo de 70% permitido pela legislação de São Paulo.
                3. Explique detalhadamente se o projeto está em conformidade com a legislação.

                Responda de forma estruturada, indicando claramente os valores encontrados e a conclusão."""

                # Análise de zoneamento
                zoneamento_prompt = """Analise cuidadosamente este projeto e identifique:
                1. O zoneamento da área onde o projeto será construído (residencial, comercial, misto, etc.).
                2. O tipo de construção proposto no projeto.
                3. Verifique se o tipo de construção proposto é permitido no zoneamento identificado.

                Responda de forma estruturada, indicando claramente o zoneamento, o tipo de construção e a conclusão sobre a compatibilidade."""

                # Análise estrutural
                estrutural_prompt = """Analise cuidadosamente os cálculos estruturais deste projeto:
                1. Identifique as especificações das vigas e pilares (dimensões, materiais, resistência).
                2. Identifique as cargas previstas para a estrutura.
                3. Verifique se as dimensões das vigas e pilares são adequadas para suportar as cargas previstas.
                4. Identifique possíveis erros ou inconsistências nos cálculos estruturais.

                Responda de forma estruturada, indicando claramente os valores encontrados, as inconsistências e a conclusão."""

                # Executar análises
                analysis = {
                    "taxa_ocupacao": conversation_chain({"question": taxa_ocupacao_prompt})["answer"],
                    "zoneamento": conversation_chain({"question": zoneamento_prompt})["answer"],
                    "calculos_estruturais": conversation_chain({"question": estrutural_prompt})["answer"]
                }

                # Adicionar resumo de conformidade
                analysis["resumo"] = {
                    "em_conformidade": "não está em conformidade" not in analysis["taxa_ocupacao"].lower() and
                                      "não é permitido" not in analysis["zoneamento"].lower() and
                                      "erro" not in analysis["calculos_estruturais"].lower() and
                                      "inconsistência" not in analysis["calculos_estruturais"].lower(),
                    "problemas_identificados": []
                }

                # Identificar problemas
                if "não está em conformidade" in analysis["taxa_ocupacao"].lower():
                    analysis["resumo"]["problemas_identificados"].append("Taxa de ocupação acima do permitido")

                if "não é permitido" in analysis["zoneamento"].lower():
                    analysis["resumo"]["problemas_identificados"].append("Incompatibilidade com zoneamento")

                if "erro" in analysis["calculos_estruturais"].lower() or "inconsistência" in analysis["calculos_estruturais"].lower():
                    analysis["resumo"]["problemas_identificados"].append("Problemas nos cálculos estruturais")

            logger.info("Análises automáticas concluídas")

            # Limpar arquivo temporário
            os.unlink(pdf_path)
            logger.info(f"Arquivo temporário removido: {pdf_path}")

            return vectorstore, conversation_chain, analysis
    except Exception as e:
        logger.error(f"Erro ao processar PDF: {str(e)}")
        st.error(f"Erro ao processar o arquivo: {str(e)}")
        return None, None, None

# Interface principal
col1, col2 = st.columns([1, 2])

with col1:
    st.subheader("Upload de Projeto")
    uploaded_file = st.file_uploader("Faça upload do seu projeto em PDF", type="pdf")

    if uploaded_file and not st.session_state.document_processed:
        result = process_pdf(uploaded_file)
        if result:
            vectorstore, conversation_chain, analysis = result
            st.session_state.vectorstore = vectorstore
            st.session_state.conversation_chain = conversation_chain
            st.session_state.analysis_results = analysis
            st.session_state.document_processed = True
            st.success("Projeto analisado com sucesso!")

    if st.session_state.document_processed:
        st.subheader("Resultados da Análise")

        # Verificar se analysis_results existe e não é None
        if not hasattr(st.session_state, 'analysis_results') or st.session_state.analysis_results is None:
            st.session_state.analysis_results = {}
            st.warning("Não foi possível carregar os resultados da análise. Por favor, tente novamente.")

        # Mostrar resumo de conformidade
        if st.session_state.analysis_results and st.session_state.analysis_results.get("resumo", {}).get("em_conformidade", False):
            st.success("✅ Projeto em conformidade com as normas!")
        else:
            st.error("❌ Projeto com problemas de conformidade")
            problemas = st.session_state.analysis_results.get("resumo", {}).get("problemas_identificados", [])
            if problemas:
                st.write("Problemas identificados:")
                for problema in problemas:
                    st.write(f"- {problema}")

        # Verificar se as chaves existem antes de acessar
        if "taxa_ocupacao" in st.session_state.analysis_results:
            with st.expander("Taxa de Ocupação", expanded=True):
                st.write(st.session_state.analysis_results["taxa_ocupacao"])
        else:
            with st.expander("Taxa de Ocupação", expanded=True):
                st.write("Informações sobre taxa de ocupação não disponíveis.")

        if "zoneamento" in st.session_state.analysis_results:
            with st.expander("Compatibilidade de Zoneamento"):
                st.write(st.session_state.analysis_results["zoneamento"])
        else:
            with st.expander("Compatibilidade de Zoneamento"):
                st.write("Informações sobre zoneamento não disponíveis.")

        if "calculos_estruturais" in st.session_state.analysis_results:
            with st.expander("Análise Estrutural"):
                st.write(st.session_state.analysis_results["calculos_estruturais"])
        else:
            with st.expander("Análise Estrutural"):
                st.write("Informações sobre cálculos estruturais não disponíveis.")

        # Opção para baixar relatório
        if st.button("Gerar Relatório PDF"):
            st.info("Funcionalidade de geração de relatório em PDF será implementada em breve.")

with col2:
    st.subheader("Pergunte sobre seu projeto")

    # Área de chat
    for message in st.session_state.chat_history:
        with st.chat_message(message["role"]):
            st.write(message["content"])

    # Input do usuário
    if user_question := st.chat_input("Digite sua pergunta sobre o projeto..."):
        if not st.session_state.document_processed:
            st.error("Por favor, faça upload de um projeto primeiro.")
        else:
            # Verificar se conversation_chain existe
            if not hasattr(st.session_state, 'conversation_chain') or st.session_state.conversation_chain is None:
                st.error("Erro: Modelo de conversação não inicializado. Por favor, faça upload do projeto novamente.")
                # Não podemos usar return aqui, então vamos usar uma variável de controle
                conversation_ready = False
            else:
                conversation_ready = True

            # Adicionar pergunta ao histórico
            st.session_state.chat_history.append({"role": "user", "content": user_question})

            # Mostrar pergunta
            with st.chat_message("user"):
                st.write(user_question)

            # Gerar resposta apenas se o modelo estiver pronto
            if conversation_ready:
                with st.chat_message("assistant"):
                    with st.spinner("Analisando..."):
                        try:
                            response = st.session_state.conversation_chain({"question": user_question})
                            st.write(response["answer"])
                            # Adicionar resposta ao histórico
                            st.session_state.chat_history.append({"role": "assistant", "content": response["answer"]})
                        except Exception as e:
                            error_msg = f"Erro ao processar sua pergunta: {str(e)}"
                            st.error(error_msg)
                            # Adicionar erro ao histórico
                            st.session_state.chat_history.append({"role": "assistant", "content": error_msg})
            else:
                # Adicionar mensagem de erro ao histórico
                error_msg = "Não foi possível processar sua pergunta. O modelo de conversação não está inicializado."
                with st.chat_message("assistant"):
                    st.error(error_msg)
                st.session_state.chat_history.append({"role": "assistant", "content": error_msg})

# Rodapé
st.markdown("---")
st.markdown("© 2023 ConstruAI Analyzer - Análise automatizada de projetos arquitetônicos")
