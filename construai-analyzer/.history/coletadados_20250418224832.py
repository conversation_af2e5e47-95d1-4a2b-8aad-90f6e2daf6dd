import requests
from bs4 import BeautifulSoup
import pandas as pd
import os
import time
import logging
from concurrent.futures import ThreadPoolExecutor
from dotenv import load_dotenv

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("coletadados.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("coletadados")

# Carregar variáveis de ambiente
load_dotenv()

# Configurações
base_url = "https://geosampa.prefeitura.sp.gov.br/"
output_dir = "projetos_data"
max_workers = 5
max_retries = 3
retry_delay = 2  # segundos

# Criar diretórios necessários
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    logger.info(f"Diretório {output_dir} criado com sucesso")
else:
    logger.info(f"Diretório {output_dir} já existe")

# Criar subdiretórios para organização
pdf_dir = os.path.join(output_dir, "pdfs")
if not os.path.exists(pdf_dir):
    os.makedirs(pdf_dir)
    logger.info(f"Diretório {pdf_dir} criado com sucesso")

# Função para baixar um PDF com retry
def download_pdf(url, filename):
    for attempt in range(max_retries):
        try:
            response = requests.get(url, stream=True, timeout=30)
            if response.status_code == 200:
                file_path = os.path.join(pdf_dir, filename)
                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                logger.info(f"Downloaded: {filename}")
                return True
            else:
                logger.warning(f"Failed to download {url}: Status code {response.status_code}")
                time.sleep(retry_delay)
        except Exception as e:
            logger.error(f"Error downloading {url}: {str(e)}")
            time.sleep(retry_delay)

    logger.error(f"Failed to download {url} after {max_retries} attempts")
    return False

# Função para coletar links de uma página
def collect_pdf_links(page_number):
    for attempt in range(max_retries):
        try:
            # URL para a página de planos de obra (ajuste conforme a estrutura real do site)
            url = f"{base_url}PaginasPublicas/downloadArquivo.aspx?origem=MAPAS&tipo=PLANTAS&page={page_number}"
            logger.info(f"Acessando URL: {url}")

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = requests.get(url, headers=headers, timeout=30)
            if response.status_code != 200:
                logger.warning(f"Status code inesperado: {response.status_code}")
                time.sleep(retry_delay)
                continue

            soup = BeautifulSoup(response.content, 'html.parser')

            # Encontrar todos os links para PDFs (ajuste o seletor conforme necessário)
            links = []
            for a in soup.find_all('a'):
                if a.has_attr('href') and '.pdf' in a['href'].lower():
                    full_url = a['href'] if a['href'].startswith('http') else base_url + a['href']
                    filename = full_url.split('/')[-1]
                    # Adicionar metadados básicos
                    metadata = {
                        'title': a.get_text().strip() if a.get_text() else filename,
                        'source_page': page_number,
                        'url': full_url
                    }
                    links.append((full_url, filename, metadata))

            logger.info(f"Encontrados {len(links)} links na página {page_number}")
            return links

        except Exception as e:
            logger.error(f"Erro ao coletar links da página {page_number}: {str(e)}")
            time.sleep(retry_delay)

    logger.error(f"Falha ao coletar links da página {page_number} após {max_retries} tentativas")
    return []

def main():
    # Coletar links de múltiplas páginas
    all_links = []
    target_count = 5000  # Meta de 5.000 documentos
    max_pages = 200      # Limite de páginas para evitar loops infinitos

    logger.info("Iniciando coleta de links de projetos...")

    for page in range(1, max_pages + 1):
        logger.info(f"Coletando links da página {page}...")
        page_links = collect_pdf_links(page)
        all_links.extend(page_links)

        # Verificar se já atingimos a meta
        if len(all_links) >= target_count:
            logger.info(f"Meta de {target_count} links atingida!")
            break

        # Pausa para não sobrecarregar o servidor
        time.sleep(2)

    logger.info(f"Total de links coletados: {len(all_links)}")

    # Salvar metadados dos links em CSV antes do download
    metadata_df = pd.DataFrame([
        {
            'url': link[0],
            'filename': link[1],
            **link[2]  # Desempacotar os metadados adicionais
        } for link in all_links
    ])

    metadata_path = os.path.join(output_dir, "metadata.csv")
    metadata_df.to_csv(metadata_path, index=False)
    logger.info(f"Metadados salvos em {metadata_path}")

    # Baixar PDFs usando multithreading
    logger.info("Iniciando download dos PDFs...")
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Passar apenas URL e filename para a função de download
        download_tasks = [(link[0], link[1]) for link in all_links]
        results = list(executor.map(lambda x: download_pdf(x[0], x[1]), download_tasks))

    success_count = sum(results)
    logger.info(f"Total de PDFs baixados com sucesso: {success_count}/{len(all_links)}")

    # Atualizar metadados com status de download
    metadata_df['download_success'] = results
    metadata_df.to_csv(metadata_path, index=False)
    logger.info(f"Metadados atualizados com status de download")

    return success_count

# Executar o script se for o arquivo principal
if __name__ == "__main__":
    main()