import requests
from bs4 import BeautifulSoup
import pandas as pd
import os 
import time
from concurrent.futures import ThreadPoolExecutor

base_url = "https://geosampa.prefeitura.sp.gov.br/"
output_dir = "projetos_data"
max_workers = 5

if not os.path.exists(output_dir):
  os.makedirs(output_dir)

# Função para baixar um PDF
def download_pdf(url, filename):
    try:
        response = requests.get(url, stream=True)
        if response.status_code == 200:
            with open(f"{output_dir}/{filename}", 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            print(f"Downloaded: {filename}")
            return True
        else:
            print(f"Failed to download {url}: Status code {response.status_code}")
            return False
    except Exception as e:
        print(f"Error downloading {url}: {str(e)}")
        return False
    
# Função para coletar links de uma página
def collect_pdf_links(page_number):
    try:
        url = f"{base_url}planos-obra?page={page_number}"
        response = requests.get(url)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Encontrar todos os links para PDFs (ajuste o seletor conforme necessário)
        links = []
        for a in soup.find_all('a'):
            if a.has_attr('href') and '.pdf' in a['href']:
                full_url = a['href'] if a['href'].startswith('http') else base_url + a['href']
                filename = full_url.split('/')[-1]
                links.append((full_url, filename))
        
        return links
    except Exception as e:
        print(f"Error collecting links from page {page_number}: {str(e)}")
        return []


# Coletar links de múltiplas páginas
all_links = []
for page in range(1, 101):  # Ajuste conforme necessário para atingir 5.000 documentos
    print(f"Coletando links da página {page}...")
    page_links = collect_pdf_links(page)
    all_links.extend(page_links)
    time.sleep(1)  # Pausa para não sobrecarregar o servidor