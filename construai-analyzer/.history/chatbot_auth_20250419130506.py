#!/usr/bin/env python3
"""
ConstruAI Analyzer - Chatbot com Autenticação
Este módulo implementa a interface de chatbot com autenticação.
"""

import os
import streamlit as st
import logging
from datetime import datetime
from auth import AuthManager

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("chatbot_auth.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("chatbot_auth")

# Inicializar gerenciador de autenticação
auth_manager = AuthManager()

def init_session_state():
    """Inicializa o estado da sessão."""
    if "authenticated" not in st.session_state:
        st.session_state.authenticated = False
    if "username" not in st.session_state:
        st.session_state.username = None
    if "user_info" not in st.session_state:
        st.session_state.user_info = None
    if "token" not in st.session_state:
        st.session_state.token = None
    if "messages" not in st.session_state:
        st.session_state.messages = []

def login_page():
    """Renderiza a página de login."""
    st.title("ConstruAI Analyzer - Login")

    # Adicionar logo ou imagem (se existir)
    if os.path.exists("logo.png"):
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            st.image("logo.png", width=200)

    # Mensagem de boas-vindas
    st.markdown("""
    <div style='text-align: center; margin-bottom: 30px;'>
    <p>Bem-vindo ao ConstruAI Analyzer, sua plataforma de análise de conformidade para projetos arquitetônicos.</p>
    </div>
    """, unsafe_allow_html=True)

    tab1, tab2 = st.tabs(["Login", "Registrar"])

    with tab1:
        with st.form("login_form"):
            st.markdown("<h3 style='text-align: center;'>Acesse sua conta</h3>", unsafe_allow_html=True)

            identifier = st.text_input("Nome de usuário ou E-mail")
            password = st.text_input("Senha", type="password")

            col1, col2, col3 = st.columns([1, 2, 1])
            with col2:
                submit = st.form_submit_button("Entrar", use_container_width=True)

            if submit:
                if identifier and password:
                    success, token = auth_manager.login(identifier, password)
                    if success:
                        st.session_state.authenticated = True
                        # Obter o nome de usuário real (pode ter sido login por e-mail)
                        valid, username = auth_manager.validate_session(token)
                        st.session_state.username = username
                        st.session_state.token = token
                        st.session_state.user_info = auth_manager.get_user_info(username)
                        st.success(f"Bem-vindo, {st.session_state.user_info['name']}!")
                        st.rerun()
                    else:
                        st.error("Nome de usuário/e-mail ou senha incorretos.")
                else:
                    st.warning("Por favor, preencha todos os campos.")

    with tab2:
        with st.form("register_form"):
            st.markdown("<h3 style='text-align: center;'>Crie sua conta</h3>", unsafe_allow_html=True)

            col1, col2 = st.columns(2)
            with col1:
                name = st.text_input("Nome completo")
            with col2:
                email = st.text_input("E-mail")

            new_username = st.text_input("Nome de usuário", key="new_username",
                                       help="Mínimo de 3 caracteres, sem espaços")

            col1, col2 = st.columns(2)
            with col1:
                new_password = st.text_input("Senha", type="password", key="new_password",
                                           help="Mínimo de 6 caracteres, inclua números")
            with col2:
                confirm_password = st.text_input("Confirmar senha", type="password")

            # Termos de uso
            terms_accepted = st.checkbox("Eu concordo com os Termos de Uso e Política de Privacidade")

            col1, col2, col3 = st.columns([1, 2, 1])
            with col2:
                submit = st.form_submit_button("Registrar", use_container_width=True)

            if submit:
                if not terms_accepted:
                    st.error("Você precisa aceitar os Termos de Uso para continuar.")
                    return

                if new_username and new_password and confirm_password and name and email:
                    if new_password != confirm_password:
                        st.error("As senhas não coincidem.")
                    else:
                        success, message = auth_manager.register_user(
                            username=new_username,
                            password=new_password,
                            name=name,
                            email=email
                        )
                        if success:
                            st.success(message)
                            # Mostrar botão para ir para o login
                            if st.button("Ir para Login"):
                                st.switch_page("chatbot_auth.py")
                        else:
                            st.error(message)
                else:
                    st.warning("Por favor, preencha todos os campos.")

        # Informações adicionais
        st.markdown("""
        <div style='text-align: center; margin-top: 20px; font-size: 0.8em;'>
        <p>Ao se registrar, você concorda em receber comunicações sobre o ConstruAI Analyzer.</p>
        <p>Suas informações pessoais serão tratadas conforme nossa <a href='#'>Política de Privacidade</a>.</p>
        </div>
        """, unsafe_allow_html=True)

def main_app():
    """Renderiza a aplicação principal."""
    st.title("ConstruAI Analyzer - Análise de Projetos")

    # Sidebar com informações do usuário
    with st.sidebar:
        st.write(f"Usuário: {st.session_state.user_info['name']}")
        st.write(f"E-mail: {st.session_state.user_info['email']}")
        st.write(f"Função: {st.session_state.user_info['role']}")

        if st.button("Sair"):
            auth_manager.logout(st.session_state.token)
            st.session_state.authenticated = False
            st.session_state.username = None
            st.session_state.token = None
            st.session_state.user_info = None
            st.rerun()

    # Abas da aplicação
    tab1, tab2, tab3 = st.tabs(["Análise de Projetos", "Histórico", "Configurações"])

    with tab1:
        st.header("Análise de Projetos")

        uploaded_file = st.file_uploader("Envie seu projeto arquitetônico (PDF)", type=["pdf", "txt"])

        if uploaded_file is not None:
            # Salvar o arquivo temporariamente
            temp_dir = "temp_uploads"
            os.makedirs(temp_dir, exist_ok=True)
            file_path = os.path.join(temp_dir, uploaded_file.name)

            with open(file_path, "wb") as f:
                f.write(uploaded_file.getbuffer())

            st.success(f"Arquivo carregado: {uploaded_file.name}")

            if st.button("Analisar Projeto"):
                with st.spinner("Analisando projeto..."):
                    # Importar o analisador de projetos
                    from pdf_analyzer import ProjectAnalyzer

                    # Analisar o projeto
                    analyzer = ProjectAnalyzer()
                    result = analyzer.analyze_project(file_path)

                    # Exibir resultados
                    if "error" in result:
                        st.error(f"Erro ao analisar projeto: {result['error']}")
                    else:
                        project_data = result["project_data"]
                        compliance = result["compliance"]

                        # Dados do projeto
                        st.subheader("Dados do Projeto")
                        col1, col2 = st.columns(2)
                        with col1:
                            st.metric("Área do Terreno", f"{project_data['area_terreno']} m²")
                            st.metric("Taxa de Ocupação", f"{project_data['taxa_ocupacao']}%")
                            st.metric("Zoneamento", project_data['zoneamento'])
                        with col2:
                            st.metric("Área Construída", f"{project_data['area_construida']} m²")
                            st.metric("Coef. Aproveitamento", project_data['coeficiente_aproveitamento'])
                            st.metric("Tipo de Construção", project_data['tipo_construcao'])

                        # Conformidade
                        st.subheader("Análise de Conformidade")
                        if compliance["em_conformidade"]:
                            st.success("✅ O projeto está em conformidade com as normas!")
                        else:
                            st.error("❌ O projeto apresenta problemas de conformidade:")
                            for problema in compliance["problemas_identificados"]:
                                st.write(f"- {problema}")

                        # Gerar relatório
                        if st.button("Gerar Relatório PDF"):
                            with st.spinner("Gerando relatório..."):
                                from report_generator import generate_report

                                output_dir = "analises"
                                os.makedirs(output_dir, exist_ok=True)

                                output_file = os.path.join(output_dir, f"{os.path.basename(file_path)}_report.pdf")
                                success = generate_report(result, output_file)

                                if success:
                                    st.success(f"Relatório gerado com sucesso!")
                                    with open(output_file, "rb") as f:
                                        st.download_button(
                                            label="Baixar Relatório",
                                            data=f,
                                            file_name=f"{os.path.basename(file_path)}_report.pdf",
                                            mime="application/pdf"
                                        )
                                else:
                                    st.error("Erro ao gerar relatório.")

    with tab2:
        st.header("Histórico de Análises")
        st.info("Em desenvolvimento. Esta funcionalidade estará disponível em breve.")

    with tab3:
        st.header("Configurações")

        # Alterar senha
        st.subheader("Alterar Senha")
        with st.form("change_password_form"):
            current_password = st.text_input("Senha atual", type="password")
            new_password = st.text_input("Nova senha", type="password")
            confirm_new_password = st.text_input("Confirmar nova senha", type="password")
            submit = st.form_submit_button("Alterar Senha")

            if submit:
                if current_password and new_password and confirm_new_password:
                    if new_password != confirm_new_password:
                        st.error("As novas senhas não coincidem.")
                    else:
                        success = auth_manager.change_password(
                            username=st.session_state.username,
                            current_password=current_password,
                            new_password=new_password
                        )
                        if success:
                            st.success("Senha alterada com sucesso!")
                        else:
                            st.error("Erro ao alterar senha. Verifique se a senha atual está correta.")
                else:
                    st.warning("Por favor, preencha todos os campos.")

def main():
    """Função principal."""
    st.set_page_config(
        page_title="ConstruAI Analyzer",
        page_icon="🏢",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    init_session_state()

    if st.session_state.authenticated:
        # Verificar se o token ainda é válido
        valid, username = auth_manager.validate_session(st.session_state.token)
        if valid:
            main_app()
        else:
            st.session_state.authenticated = False
            st.session_state.username = None
            st.session_state.token = None
            st.session_state.user_info = None
            login_page()
    else:
        login_page()

if __name__ == "__main__":
    main()
