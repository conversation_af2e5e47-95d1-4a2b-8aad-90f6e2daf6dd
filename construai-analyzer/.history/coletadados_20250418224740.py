import requests
from bs4 import BeautifulSoup
import pandas as pd
import os
import time
import logging
from concurrent.futures import ThreadPoolExecutor
from dotenv import load_dotenv

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("coletadados.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("coletadados")

# Carregar variáveis de ambiente
load_dotenv()

# Configurações
base_url = "https://geosampa.prefeitura.sp.gov.br/"
output_dir = "projetos_data"
max_workers = 5
max_retries = 3
retry_delay = 2  # segundos

# Criar diretórios necessários
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    logger.info(f"Diretório {output_dir} criado com sucesso")
else:
    logger.info(f"Diretório {output_dir} já existe")

# Criar subdiretórios para organização
pdf_dir = os.path.join(output_dir, "pdfs")
if not os.path.exists(pdf_dir):
    os.makedirs(pdf_dir)
    logger.info(f"Diretório {pdf_dir} criado com sucesso")

# Função para baixar um PDF
def download_pdf(url, filename):
    try:
        response = requests.get(url, stream=True)
        if response.status_code == 200:
            with open(f"{output_dir}/{filename}", 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            print(f"Downloaded: {filename}")
            return True
        else:
            print(f"Failed to download {url}: Status code {response.status_code}")
            return False
    except Exception as e:
        print(f"Error downloading {url}: {str(e)}")
        return False

# Função para coletar links de uma página
def collect_pdf_links(page_number):
    try:
        url = f"{base_url}planos-obra?page={page_number}"
        response = requests.get(url)
        soup = BeautifulSoup(response.content, 'html.parser')

        # Encontrar todos os links para PDFs (ajuste o seletor conforme necessário)
        links = []
        for a in soup.find_all('a'):
            if a.has_attr('href') and '.pdf' in a['href']:
                full_url = a['href'] if a['href'].startswith('http') else base_url + a['href']
                filename = full_url.split('/')[-1]
                links.append((full_url, filename))

        return links
    except Exception as e:
        print(f"Error collecting links from page {page_number}: {str(e)}")
        return []

# Coletar links de múltiplas páginas
all_links = []
for page in range(1, 101):  # Ajuste conforme necessário para atingir 5.000 documentos
    print(f"Coletando links da página {page}...")
    page_links = collect_pdf_links(page)
    all_links.extend(page_links)
    time.sleep(1)  # Pausa para não sobrecarregar o servidor

print(f"Total de links coletados: {len(all_links)}")

# Baixar PDFs usando multithreading
with ThreadPoolExecutor(max_workers=max_workers) as executor:
    results = list(executor.map(lambda x: download_pdf(x[0], x[1]), all_links))

print(f"Total de PDFs baixados: {sum(results)}")

# Salvar metadados dos links em CSV
pd.DataFrame(all_links, columns=['url', 'filename']).to_csv(f"{output_dir}/metadata.csv", index=False)