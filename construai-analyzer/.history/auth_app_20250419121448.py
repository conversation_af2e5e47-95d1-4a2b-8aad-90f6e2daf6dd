#!/usr/bin/env python3
"""
ConstruAI Analyzer - Aplicação principal com autenticação
"""

import streamlit as st
import os
import logging
from datetime import datetime
from dotenv import load_dotenv

# Configurar página - deve ser a primeira chamada do Streamlit
st.set_page_config(
    page_title="ConstruAI Analyzer",
    page_icon="🏗️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Importar sistema de autenticação
from login_page import show_login_page, show_user_info, init_session
from auth_manager import auth_manager

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("main.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("auth_app")

# Carregar variáveis de ambiente
load_dotenv()

def check_environment():
    """Verifica se o ambiente está configurado corretamente."""
    logger.info("Verificando ambiente...")

    # Verificar se as variáveis de ambiente necessárias estão definidas
    if not os.getenv("GOOGLE_API_KEY"):
        logger.warning("GOOGLE_API_KEY não encontrada no arquivo .env")
        return False

    logger.info("Ambiente configurado com sucesso!")
    return True

def main():
    """Função principal."""
    # Verificar ambiente
    if not check_environment():
        st.error("Erro ao configurar o ambiente. Verifique o arquivo .env.")
        return

    # Inicializar sessão de autenticação
    init_session()

    # Verificar se o usuário está autenticado
    if not st.session_state.authenticated:
        show_login_page()
        return

    # Mostrar informações do usuário na barra lateral
    show_user_info()

    # Adicionar informações de preço na barra lateral
    st.sidebar.title("Seu Plano")
    plan_name = auth_manager.get_plans()[st.session_state.user["plan"]]["name"]
    st.sidebar.info(f"""
    ### Plano atual: {plan_name}
    Créditos restantes: {st.session_state.user["credits"]}

    Para mais informações sobre planos, entre em contato.
    """)

    st.sidebar.title("Sobre")
    st.sidebar.info("""
    ### ConstruAI Analyzer
    Análise automatizada de projetos arquitetônicos usando IA.

    Detectamos:
    - Violações de taxa de ocupação
    - Incompatibilidades com zoneamento
    - Erros em cálculos estruturais
    """)

    # Interface principal
    st.title("ConstruAI Analyzer")
    st.markdown("Envie seu projeto em PDF e receba a análise em minutos.")

    # Inicializar variáveis de estado da sessão
    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []
    if 'document_processed' not in st.session_state:
        st.session_state.document_processed = False
    if 'analysis_results' not in st.session_state:
        st.session_state.analysis_results = {}

    # Importar o analisador de PDF
    from pdf_analyzer import ProjectAnalyzer

    # Interface principal
    col1, col2 = st.columns([1, 2])

    with col1:
        st.subheader("Upload de Projeto")
        uploaded_file = st.file_uploader("Faça upload do seu projeto em PDF", type="pdf")

        if uploaded_file and not st.session_state.document_processed:
            # Verificar se o usuário tem créditos suficientes
            if st.session_state.user["credits"] <= 0:
                st.error("Você não tem créditos suficientes para analisar este projeto. Por favor, adquira mais créditos.")
            else:
                # Processar o arquivo
                with st.spinner("Processando seu projeto... Isso pode levar até 3 minutos."):
                    # Importar a função de processamento
                    from chatbot_module import process_pdf

                    # Processar o arquivo
                    result = process_pdf(uploaded_file)

                    if result:
                        vectorstore, conversation_chain, analysis = result
                        st.session_state.vectorstore = vectorstore
                        st.session_state.conversation_chain = conversation_chain
                        st.session_state.analysis_results = analysis
                        st.session_state.document_processed = True

                        # Usar um crédito
                        auth_manager.use_credit(st.session_state.user["email"])
                        # Atualizar créditos na sessão
                        st.session_state.user["credits"] -= 1

                        st.success("Projeto analisado com sucesso!")
                        st.rerun()

    with col2:
        if st.session_state.document_processed:
            # Mostrar resultados da análise
            st.subheader("Resultados da Análise")

            # Verificar se analysis_results existe e não é None
            if not hasattr(st.session_state, 'analysis_results') or st.session_state.analysis_results is None:
                st.session_state.analysis_results = {}
                st.warning("Não foi possível carregar os resultados da análise. Por favor, tente novamente.")

            # Mostrar resumo de conformidade
            if st.session_state.analysis_results and st.session_state.analysis_results.get("resumo", {}).get("em_conformidade", False):
                st.success("✅ Projeto em conformidade com as normas!")
            else:
                st.error("❌ Projeto com problemas de conformidade")
                problemas = st.session_state.analysis_results.get("resumo", {}).get("problemas_identificados", [])
                if problemas:
                    st.write("Problemas identificados:")
                    for problema in problemas:
                        st.write(f"- {problema}")

            # Verificar se as chaves existem antes de acessar
            if "taxa_ocupacao" in st.session_state.analysis_results:
                with st.expander("Taxa de Ocupação", expanded=True):
                    st.write(st.session_state.analysis_results["taxa_ocupacao"])
            else:
                with st.expander("Taxa de Ocupação", expanded=True):
                    st.write("Informações sobre taxa de ocupação não disponíveis.")

            if "zoneamento" in st.session_state.analysis_results:
                with st.expander("Compatibilidade de Zoneamento"):
                    st.write(st.session_state.analysis_results["zoneamento"])
            else:
                with st.expander("Compatibilidade de Zoneamento"):
                    st.write("Informações sobre zoneamento não disponíveis.")

            if "calculos_estruturais" in st.session_state.analysis_results:
                with st.expander("Análise Estrutural"):
                    st.write(st.session_state.analysis_results["calculos_estruturais"])
            else:
                with st.expander("Análise Estrutural"):
                    st.write("Informações sobre cálculos estruturais não disponíveis.")

            # Visualizações
            st.subheader("Visualizações")

            # Importar funções de visualização
            from chatbot_module import create_taxa_ocupacao_chart, create_zoneamento_chart, create_compliance_gauge

            # Criar abas para diferentes visualizações
            viz_tabs = st.tabs(["Taxa de Ocupação", "Zoneamento", "Conformidade"])

            with viz_tabs[0]:
                # Gráfico de taxa de ocupação
                if "project_data" in st.session_state.analysis_results and st.session_state.analysis_results.get("project_data", {}).get("taxa_ocupacao"):
                    taxa_ocupacao = st.session_state.analysis_results.get("project_data", {}).get("taxa_ocupacao", 0)
                    fig = create_taxa_ocupacao_chart(taxa_ocupacao)
                    st.pyplot(fig)
                else:
                    st.info("Dados de taxa de ocupação não disponíveis.")

            with viz_tabs[1]:
                # Gráfico de zoneamento
                if "project_data" in st.session_state.analysis_results and st.session_state.analysis_results.get("project_data", {}).get("zoneamento"):
                    zoneamento = st.session_state.analysis_results.get("project_data", {}).get("zoneamento", "")
                    fig = create_zoneamento_chart(zoneamento)
                    st.pyplot(fig)
                else:
                    st.info("Dados de zoneamento não disponíveis.")

            with viz_tabs[2]:
                # Gráfico de conformidade
                em_conformidade = st.session_state.analysis_results.get("resumo", {}).get("em_conformidade", False)
                problemas = st.session_state.analysis_results.get("resumo", {}).get("problemas_identificados", [])
                fig = create_compliance_gauge(em_conformidade, problemas)
                st.pyplot(fig)

                # Mostrar lista de problemas
                if problemas:
                    st.write("Problemas identificados:")
                    for problema in problemas:
                        st.write(f"- {problema}")

            # Opção para baixar relatório
            if st.button("Gerar Relatório PDF"):
                try:
                    from report_generator import generate_report

                    # Verificar se temos dados suficientes para gerar o relatório
                    if not st.session_state.analysis_results or "project_data" not in st.session_state.analysis_results:
                        st.error("Não há dados suficientes para gerar o relatório. Certifique-se de que o projeto foi analisado corretamente.")
                        can_generate_report = False
                    else:
                        can_generate_report = True

                    # Continuar apenas se tivermos dados suficientes
                    if can_generate_report:
                        # Criar diretório para relatórios
                        os.makedirs("relatorios", exist_ok=True)

                        # Nome do arquivo baseado no timestamp
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        report_path = f"relatorios/relatorio_{timestamp}.pdf"

                        # Gerar relatório
                        with st.spinner("Gerando relatório PDF..."):
                            # Criar uma cópia dos dados para evitar problemas de referência
                            analysis_data = dict(st.session_state.analysis_results)

                            # Garantir que os campos obrigatórios existam
                            if "project_data" not in analysis_data:
                                analysis_data["project_data"] = {}
                            if "compliance" not in analysis_data:
                                analysis_data["compliance"] = {"em_conformidade": False, "problemas_identificados": ["Dados insuficientes"]}

                            result = generate_report(analysis_data, report_path)

                        if result and os.path.exists(report_path):
                            # Criar link para download
                            with open(report_path, "rb") as file:
                                btn = st.download_button(
                                    label="Baixar Relatório PDF",
                                    data=file,
                                    file_name=f"relatorio_projeto_{timestamp}.pdf",
                                    mime="application/pdf"
                                )
                            st.success("Relatório gerado com sucesso!")
                        else:
                            st.error("Erro ao gerar relatório. Verifique os logs para mais detalhes.")
                except Exception as e:
                    st.error(f"Erro ao gerar relatório: {str(e)}")

            # Chat com o Projeto
            st.subheader("Chat com o Projeto")

            # Exibir histórico de chat
            for message in st.session_state.chat_history:
                with st.chat_message(message["role"]):
                    st.write(message["content"])

            # Input do usuário
            if user_question := st.chat_input("Digite sua pergunta sobre o projeto..."):
                # Verificar se conversation_chain existe
                if not hasattr(st.session_state, 'conversation_chain') or st.session_state.conversation_chain is None:
                    st.error("Erro: Modelo de conversação não inicializado. Por favor, faça upload do projeto novamente.")
                    conversation_ready = False
                else:
                    conversation_ready = True

                # Adicionar pergunta ao histórico
                st.session_state.chat_history.append({"role": "user", "content": user_question})

                # Mostrar pergunta
                with st.chat_message("user"):
                    st.write(user_question)

                # Gerar resposta apenas se o modelo estiver pronto
                if conversation_ready:
                    with st.chat_message("assistant"):
                        with st.spinner("Analisando..."):
                            try:
                                response = st.session_state.conversation_chain({"question": user_question})
                                st.write(response["answer"])
                                # Adicionar resposta ao histórico
                                st.session_state.chat_history.append({"role": "assistant", "content": response["answer"]})
                            except Exception as e:
                                error_msg = f"Erro ao processar sua pergunta: {str(e)}"
                                st.error(error_msg)
                                # Adicionar erro ao histórico
                                st.session_state.chat_history.append({"role": "assistant", "content": error_msg})
                else:
                    # Adicionar mensagem de erro ao histórico
                    error_msg = "Não foi possível processar sua pergunta. O modelo de conversação não está inicializado."
                    with st.chat_message("assistant"):
                        st.error(error_msg)
                    st.session_state.chat_history.append({"role": "assistant", "content": error_msg})

if __name__ == "__main__":
    main()
